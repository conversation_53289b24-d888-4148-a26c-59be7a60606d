"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Radio,
  TowerControlIcon as Tower,
  Signal,
  Activity,
  MapPin,
  BarChart3,
  TrendingUp,
  Wifi,
  Zap,
  RefreshCw,
} from "lucide-react"

export default function NetworkPage() {
  const [networkData, setNetworkData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  const fetchData = async () => {
    try {
      const response = await fetch("/api/network-usage.json")
      const data = await response.json()
      setNetworkData(data)
      setLastUpdate(new Date())
    } catch (error) {
      console.error("Error fetching network data:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()

    // Real-time updates every 30 seconds
    const interval = setInterval(fetchData, 30000)
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-cyan-500"></div>
      </div>
    )
  }

  const totalCalls = networkData?.cells?.reduce((sum: number, cell: any) => sum + cell.totalCalls, 0) || 0
  const totalDistinctCalls = networkData?.cells?.reduce((sum: number, cell: any) => sum + cell.distinctCalls, 0) || 0

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Network Usage Analytics</h1>
          <p className="text-gray-400 mt-2">Real-time network performance and cell tower monitoring</p>
          <p className="text-xs text-gray-500 mt-1">Last updated: {lastUpdate.toLocaleTimeString()}</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm" onClick={fetchData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-400/20">
            <Activity className="w-4 h-4 mr-1" />
            Live
          </Badge>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active Cell Sites</p>
                <p className="text-2xl font-bold text-cyan-400">{networkData?.uniqueCellSites}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <p className="text-sm text-green-400">100% Online</p>
                </div>
              </div>
              <Tower className="w-8 h-8 text-cyan-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Network Calls</p>
                <p className="text-2xl font-bold text-blue-400">{totalCalls}</p>
                <p className="text-sm text-gray-400">Across all cells</p>
              </div>
              <Signal className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Distinct Calls</p>
                <p className="text-2xl font-bold text-purple-400">{totalDistinctCalls}</p>
                <p className="text-sm text-gray-400">Unique sessions</p>
              </div>
              <Wifi className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Network Efficiency</p>
                <p className="text-2xl font-bold text-green-400">
                  {totalCalls > 0 ? ((totalDistinctCalls / totalCalls) * 100).toFixed(1) : 0}%
                </p>
                <p className="text-sm text-gray-400">Call optimization</p>
              </div>
              <Zap className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Network Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-gray-900 border border-gray-800">
          <TabsTrigger value="overview" className="data-[state=active]:bg-cyan-600">
            Network Overview
          </TabsTrigger>
          <TabsTrigger value="cells" className="data-[state=active]:bg-blue-600">
            Cell Tower Details
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-green-600">
            Performance Metrics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-cyan-400">
                  <Radio className="w-5 h-5 mr-2" />
                  Network Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {networkData?.cells?.map((cell: any, index: number) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">{cell.cellId}</span>
                      <span className="font-mono text-cyan-400">{cell.totalCalls} calls</span>
                    </div>
                    <Progress value={totalCalls > 0 ? (cell.totalCalls / totalCalls) * 100 : 0} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-purple-400">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Call Quality Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {networkData?.cells?.map((cell: any, index: number) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Tower className="w-4 h-4 text-gray-400" />
                      <span className="font-mono text-sm text-gray-400">{cell.cellId}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-cyan-400">{cell.totalCalls} total</p>
                        <p className="text-xs text-purple-400">{cell.distinctCalls} distinct</p>
                      </div>
                      <Badge
                        variant="outline"
                        className={
                          cell.totalCalls > 3
                            ? "text-green-400 border-green-400/20"
                            : cell.totalCalls > 0
                              ? "text-yellow-400 border-yellow-400/20"
                              : "text-red-400 border-red-400/20"
                        }
                      >
                        {cell.totalCalls > 3 ? "High" : cell.totalCalls > 0 ? "Medium" : "Low"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cells" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {networkData?.cells?.map((cell: any, index: number) => (
              <Card key={index} className="bg-gray-900 border-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-blue-400">
                    <Tower className="w-5 h-5 mr-2" />
                    {cell.cellId}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Calls</span>
                    <span className="font-mono text-2xl text-blue-400">{cell.totalCalls}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Distinct Calls</span>
                    <span className="font-mono text-purple-400">{cell.distinctCalls}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Efficiency</span>
                    <span className="font-mono text-green-400">
                      {cell.totalCalls > 0 ? ((cell.distinctCalls / cell.totalCalls) * 100).toFixed(1) : 0}%
                    </span>
                  </div>

                  <div className="pt-4 border-t border-gray-800">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Status</span>
                      <Badge
                        className={
                          cell.totalCalls > 0
                            ? "bg-green-900/20 text-green-400 border-green-400/20"
                            : "bg-red-900/20 text-red-400 border-red-400/20"
                        }
                      >
                        {cell.totalCalls > 0 ? "Active" : "Idle"}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Signal Strength</span>
                    <div className="flex items-center space-x-1">
                      {[1, 2, 3, 4, 5].map((bar) => (
                        <div
                          key={bar}
                          className={`w-1 h-3 rounded ${
                            bar <= (cell.totalCalls > 3 ? 5 : cell.totalCalls > 0 ? 3 : 1)
                              ? "bg-green-400"
                              : "bg-gray-600"
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-green-400">Network Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Network Availability</span>
                  <Badge className="bg-green-900/20 text-green-400 border-green-400/20">99.9%</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Average Response Time</span>
                  <span className="font-mono text-cyan-400">45ms</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Peak Load Capacity</span>
                  <span className="font-mono text-purple-400">85%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Error Rate</span>
                  <span className="font-mono text-red-400">0.1%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Throughput</span>
                  <span className="font-mono text-yellow-400">1.2 Gbps</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-orange-400">Cell Tower Health</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {networkData?.cells?.map((cell: any, index: number) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="font-mono text-sm text-gray-400">{cell.cellId}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-right">
                        <p className="text-xs text-gray-400">Load: {cell.totalCalls > 0 ? "Active" : "Idle"}</p>
                        <p className="text-xs text-gray-400">
                          Capacity: {cell.totalCalls > 3 ? "High" : cell.totalCalls > 0 ? "Medium" : "Low"}
                        </p>
                      </div>
                      <div className={`w-3 h-3 rounded-full ${cell.totalCalls > 0 ? "bg-green-400" : "bg-gray-600"}`} />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-yellow-400">Network Optimization Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2" />
                  <div>
                    <p className="text-white font-medium">Cell Load Balancing</p>
                    <p className="text-gray-400 text-sm">
                      {networkData?.cells?.find((c: any) => c.cellId === "Cellid 1")?.totalCalls > 3
                        ? "Consider redistributing traffic from Cellid 1 to optimize performance"
                        : "Current load distribution is optimal"}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2" />
                  <div>
                    <p className="text-white font-medium">Capacity Planning</p>
                    <p className="text-gray-400 text-sm">
                      Network capacity is sufficient for current load with room for 40% growth
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2" />
                  <div>
                    <p className="text-white font-medium">Maintenance Schedule</p>
                    <p className="text-gray-400 text-sm">All cell towers are operating within normal parameters</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
