"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Bell, Shield, User, Database, Download, Upload, Save, RefreshCw, Lock, Eye, EyeOff } from "lucide-react"

export default function SettingsPage() {
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    realTime: true,
  })

  const [showPassword, setShowPassword] = useState(false)
  const [settings, setSettings] = useState({
    refreshInterval: 30,
    dataRetention: 90,
    autoExport: false,
    darkMode: true,
  })

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotifications((prev) => ({ ...prev, [key]: value }))
  }

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev) => ({ ...prev, [key]: value }))
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Platform Settings</h1>
          <p className="text-gray-400 mt-2">Configure your TelecomPro analytics platform</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset to Default
          </Button>
          <Button size="sm">
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList className="bg-gray-900 border border-gray-800">
          <TabsTrigger value="notifications" className="data-[state=active]:bg-blue-600">
            <Bell className="w-4 h-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="display" className="data-[state=active]:bg-purple-600">
            <Eye className="w-4 h-4 mr-2" />
            Display
          </TabsTrigger>
          <TabsTrigger value="data" className="data-[state=active]:bg-green-600">
            <Database className="w-4 h-4 mr-2" />
            Data Management
          </TabsTrigger>
          <TabsTrigger value="security" className="data-[state=active]:bg-red-600">
            <Shield className="w-4 h-4 mr-2" />
            Security
          </TabsTrigger>
          <TabsTrigger value="account" className="data-[state=active]:bg-yellow-600">
            <User className="w-4 h-4 mr-2" />
            Account
          </TabsTrigger>
        </TabsList>

        {/* Notifications Settings */}
        <TabsContent value="notifications" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-400">
                <Bell className="w-5 h-5 mr-2" />
                Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-white">Email Notifications</Label>
                    <p className="text-sm text-gray-400">Receive alerts and reports via email</p>
                  </div>
                  <Switch
                    checked={notifications.email}
                    onCheckedChange={(value) => handleNotificationChange("email", value)}
                  />
                </div>
                <Separator className="bg-gray-800" />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-white">SMS Alerts</Label>
                    <p className="text-sm text-gray-400">Critical alerts sent to your mobile</p>
                  </div>
                  <Switch
                    checked={notifications.sms}
                    onCheckedChange={(value) => handleNotificationChange("sms", value)}
                  />
                </div>
                <Separator className="bg-gray-800" />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-white">Push Notifications</Label>
                    <p className="text-sm text-gray-400">Browser notifications for real-time updates</p>
                  </div>
                  <Switch
                    checked={notifications.push}
                    onCheckedChange={(value) => handleNotificationChange("push", value)}
                  />
                </div>
                <Separator className="bg-gray-800" />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-white">Real-time Alerts</Label>
                    <p className="text-sm text-gray-400">Instant notifications for system events</p>
                  </div>
                  <Switch
                    checked={notifications.realTime}
                    onCheckedChange={(value) => handleNotificationChange("realTime", value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-green-400">Alert Thresholds</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-white">High Call Volume (per hour)</Label>
                  <Input type="number" defaultValue="100" className="bg-gray-800 border-gray-700 text-white" />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Low Network Performance (%)</Label>
                  <Input type="number" defaultValue="85" className="bg-gray-800 border-gray-700 text-white" />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Revenue Drop (%)</Label>
                  <Input type="number" defaultValue="10" className="bg-gray-800 border-gray-700 text-white" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-purple-400">Notification Channels</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-white">Email Address</Label>
                  <Input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="bg-gray-800 border-gray-700 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">SMS Number</Label>
                  <Input
                    type="tel"
                    defaultValue="+212 6XX XXX XXX"
                    className="bg-gray-800 border-gray-700 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Slack Webhook</Label>
                  <Input
                    type="url"
                    placeholder="https://hooks.slack.com/..."
                    className="bg-gray-800 border-gray-700 text-white"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Display Settings */}
        <TabsContent value="display" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-purple-400">
                <Eye className="w-5 h-5 mr-2" />
                Display Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-white">Dark Mode</Label>
                  <p className="text-sm text-gray-400">Use dark theme throughout the platform</p>
                </div>
                <Switch
                  checked={settings.darkMode}
                  onCheckedChange={(value) => handleSettingChange("darkMode", value)}
                />
              </div>
              <Separator className="bg-gray-800" />

              <div className="space-y-2">
                <Label className="text-white">Auto-refresh Interval (seconds)</Label>
                <Input
                  type="number"
                  value={settings.refreshInterval}
                  onChange={(e) => handleSettingChange("refreshInterval", Number.parseInt(e.target.value))}
                  className="bg-gray-800 border-gray-700 text-white"
                />
                <p className="text-sm text-gray-400">How often data should refresh automatically</p>
              </div>

              <div className="space-y-2">
                <Label className="text-white">Default Dashboard View</Label>
                <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                  <option value="overview">Overview</option>
                  <option value="calls">Call Activity</option>
                  <option value="revenue">Revenue Focus</option>
                  <option value="network">Network Status</option>
                </select>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-cyan-400">Chart Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-white">Default Chart Type</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="bar">Bar Chart</option>
                    <option value="line">Line Chart</option>
                    <option value="pie">Pie Chart</option>
                    <option value="area">Area Chart</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Animation Speed</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="slow">Slow</option>
                    <option value="normal">Normal</option>
                    <option value="fast">Fast</option>
                    <option value="none">No Animation</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-orange-400">Language & Region</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-white">Language</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="en">English</option>
                    <option value="fr">Français</option>
                    <option value="ar">العربية</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Timezone</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="GMT">GMT (Casablanca)</option>
                    <option value="CET">CET (Paris)</option>
                    <option value="UTC">UTC</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Currency</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="MAD">MAD (Dirham)</option>
                    <option value="EUR">EUR (Euro)</option>
                    <option value="USD">USD (Dollar)</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Data Management */}
        <TabsContent value="data" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-green-400">
                <Database className="w-5 h-5 mr-2" />
                Data Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label className="text-white">Data Retention Period (days)</Label>
                <Input
                  type="number"
                  value={settings.dataRetention}
                  onChange={(e) => handleSettingChange("dataRetention", Number.parseInt(e.target.value))}
                  className="bg-gray-800 border-gray-700 text-white"
                />
                <p className="text-sm text-gray-400">How long to keep historical data</p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-white">Auto Export Reports</Label>
                  <p className="text-sm text-gray-400">Automatically export daily/weekly reports</p>
                </div>
                <Switch
                  checked={settings.autoExport}
                  onCheckedChange={(value) => handleSettingChange("autoExport", value)}
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-blue-400">Export Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-white">Export Format</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="csv">CSV</option>
                    <option value="xlsx">Excel</option>
                    <option value="pdf">PDF</option>
                    <option value="json">JSON</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Export Schedule</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="manual">Manual Only</option>
                  </select>
                </div>

                <Button className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Export Current Data
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-yellow-400">Backup & Restore</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-white">Backup Frequency</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Backup Location</Label>
                  <Input
                    type="text"
                    defaultValue="/backups/telecompro"
                    className="bg-gray-800 border-gray-700 text-white"
                  />
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1 bg-transparent">
                    <Upload className="w-4 h-4 mr-2" />
                    Backup Now
                  </Button>
                  <Button variant="outline" className="flex-1 bg-transparent">
                    <Download className="w-4 h-4 mr-2" />
                    Restore
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-red-400">
                <Shield className="w-5 h-5 mr-2" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label className="text-white">Current Password</Label>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter current password"
                    className="bg-gray-800 border-gray-700 text-white pr-10"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-white">New Password</Label>
                <Input
                  type="password"
                  placeholder="Enter new password"
                  className="bg-gray-800 border-gray-700 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-white">Confirm New Password</Label>
                <Input
                  type="password"
                  placeholder="Confirm new password"
                  className="bg-gray-800 border-gray-700 text-white"
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-orange-400">Two-Factor Authentication</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-white">Enable 2FA</Label>
                    <p className="text-sm text-gray-400">Add extra security to your account</p>
                  </div>
                  <Badge className="bg-red-900/20 text-red-400 border-red-400/20">Disabled</Badge>
                </div>

                <Button className="w-full">
                  <Lock className="w-4 h-4 mr-2" />
                  Setup Two-Factor Auth
                </Button>

                <div className="space-y-2">
                  <Label className="text-white">Backup Codes</Label>
                  <Button variant="outline" className="w-full bg-transparent">
                    Generate Backup Codes
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-cyan-400">Session Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-white">Session Timeout (minutes)</Label>
                  <Input type="number" defaultValue="60" className="bg-gray-800 border-gray-700 text-white" />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">IP Whitelist</Label>
                  <Input type="text" placeholder="***********/24" className="bg-gray-800 border-gray-700 text-white" />
                </div>

                <Button variant="outline" className="w-full bg-transparent">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Revoke All Sessions
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Account Settings */}
        <TabsContent value="account" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-400">
                <User className="w-5 h-5 mr-2" />
                Account Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-white">Full Name</Label>
                  <Input type="text" defaultValue="Admin User" className="bg-gray-800 border-gray-700 text-white" />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Email Address</Label>
                  <Input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="bg-gray-800 border-gray-700 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Phone Number</Label>
                  <Input
                    type="tel"
                    defaultValue="+212 6XX XXX XXX"
                    className="bg-gray-800 border-gray-700 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-white">Department</Label>
                  <select className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white">
                    <option value="admin">Administration</option>
                    <option value="technical">Technical</option>
                    <option value="analytics">Analytics</option>
                    <option value="management">Management</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-white">Bio</Label>
                <textarea
                  className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white h-20 resize-none"
                  placeholder="Brief description about yourself..."
                  defaultValue="System administrator for TelecomPro analytics platform"
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-green-400">Subscription Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Plan Type</span>
                  <Badge className="bg-green-900/20 text-green-400 border-green-400/20">Enterprise</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Status</span>
                  <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">Active</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Renewal Date</span>
                  <span className="font-mono text-white">2024-12-15</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Users</span>
                  <span className="font-mono text-white">5 / 10</span>
                </div>

                <Button variant="outline" className="w-full bg-transparent">
                  Upgrade Plan
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-purple-400">Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">API Calls This Month</span>
                  <span className="font-mono text-purple-400">12,450</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Data Processed</span>
                  <span className="font-mono text-purple-400">2.3 GB</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Reports Generated</span>
                  <span className="font-mono text-purple-400">156</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Storage Used</span>
                  <span className="font-mono text-purple-400">45%</span>
                </div>

                <Button variant="outline" className="w-full bg-transparent">
                  View Detailed Usage
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900 border-gray-800 border-red-500/20">
            <CardHeader>
              <CardTitle className="text-red-400">Danger Zone</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="text-white">Delete Account</Label>
                <p className="text-sm text-gray-400">
                  Permanently delete your account and all associated data. This action cannot be undone.
                </p>
              </div>

              <Button variant="destructive" className="bg-red-600 hover:bg-red-700">
                Delete Account
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-gray-400">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start bg-transparent">
              <Bell className="w-4 h-4 mr-2" />
              Test Notifications
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Download className="w-4 h-4 mr-2" />
              Export Settings
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Upload className="w-4 h-4 mr-2" />
              Import Settings
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <RefreshCw className="w-4 h-4 mr-2" />
              Reset All Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
