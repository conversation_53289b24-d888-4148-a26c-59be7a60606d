"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import {
  Phone,
  MessageSquare,
  Radio,
  CreditCard,
  Users,
  Download,
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  Shield,
  Target,
  Eye,
  Clock,
  MapPin,
  Smartphone,
  Bell,
  Zap,
  BarChart3,
  Globe,
} from "lucide-react"
import Link from "next/link"
import { RealTimeAlerts } from "@/components/real-time-alerts"

export default function Dashboard() {
  const [data, setData] = useState<any>({})
  const [fraudData, setFraudData] = useState<any>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [subscriberRes, rechargeRes, dealerRes, networkRes, localCallRes, internationalCallRes, smsRes, dataRes, enhancedCallRes, imeiRes, enhancedSubscriberRes] =
          await Promise.all([
            fetch("/api/subscriber-profile.json"),
            fetch("/api/recharge-payment.json"),
            fetch("/api/dealer-activations.json"),
            fetch("/api/network-usage.json"),
            fetch("/api/local-call-activity.json"),
            fetch("/api/international-call-activity.json"),
            fetch("/api/sms-activity.json"),
            fetch("/api/data-activity.json"),
            fetch("/api/enhanced-call-activity.json"),
            fetch("/api/imei-tracking.json"),
            fetch("/api/enhanced-subscriber-profile.json"),
          ])

        const [
          subscriberData,
          rechargeData,
          dealerData,
          networkData,
          localCallData,
          internationalCallData,
          smsData,
          dataData,
          enhancedCallData,
          imeiData,
          enhancedSubscriberData,
        ] = await Promise.all([
          subscriberRes.json(),
          rechargeRes.json(),
          dealerRes.json(),
          networkRes.json(),
          localCallRes.json(),
          internationalCallRes.json(),
          smsRes.json(),
          dataRes.json(),
          enhancedCallRes.json(),
          imeiRes.json(),
          enhancedSubscriberRes.json(),
        ])

        setData({
          subscriber: subscriberData,
          recharge: rechargeData,
          dealer: dealerData,
          network: networkData,
          localCall: localCallData,
          internationalCall: internationalCallData,
          sms: smsData,
          dataActivity: dataData,
        })

        setFraudData({
          calls: enhancedCallData,
          imei: imeiData,
          subscriber: enhancedSubscriberData,
        })
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  // Calculate fraud metrics
  const overallRiskScore = fraudData.subscriber?.fraudProfile?.overallRiskScore || 0
  const getRiskLevel = (score: number) => {
    if (score >= 7) return { level: "HIGH", color: "text-red-400", bgColor: "bg-red-900/20", borderColor: "border-red-400/20" }
    if (score >= 4) return { level: "MEDIUM", color: "text-yellow-400", bgColor: "bg-yellow-900/20", borderColor: "border-yellow-400/20" }
    return { level: "LOW", color: "text-green-400", bgColor: "bg-green-900/20", borderColor: "border-green-400/20" }
  }
  const riskInfo = getRiskLevel(overallRiskScore)

  return (
    <div className="p-6 space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            Fraud Detection Command Center
          </h1>
          <p className="text-gray-400 mt-2">
            Real-time fraud monitoring for subscriber {data.subscriber?.msisdn} • Risk Level:
            <span className={`ml-1 font-medium ${riskInfo.color}`}>{riskInfo.level}</span>
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge className="bg-green-900/20 text-green-400 border-green-400/20">
            <Activity className="w-4 h-4 mr-1" />
            Live Monitoring
          </Badge>
          <Button variant="outline" size="sm" className="bg-blue-900/20 border-blue-400/20 text-blue-400">
            <BarChart3 className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Critical Alert Banner */}
      {overallRiskScore >= 7 && (
        <Card className="bg-red-900/20 border-red-400/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-6 h-6 text-red-400" />
                <div>
                  <p className="text-red-400 font-semibold">🚨 Critical Fraud Alert</p>
                  <p className="text-red-300 text-sm">
                    High-risk fraud patterns detected. Risk Score: {overallRiskScore}/10 - Immediate investigation required.
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Link href="/fraud">
                  <Button size="sm" className="bg-red-600 hover:bg-red-700">
                    <Eye className="w-4 h-4 mr-1" />
                    Investigate Now
                  </Button>
                </Link>
                <Button size="sm" variant="outline" className="border-red-400/20 text-red-400">
                  <Bell className="w-4 h-4 mr-1" />
                  Create Alert
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fraud Detection Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Link href="/fraud">
          <Card className={`bg-gray-900 border-gray-800 ${riskInfo.borderColor} hover:border-opacity-60 transition-colors cursor-pointer`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-400">Fraud Risk Score</p>
                  <p className={`text-xl font-bold ${riskInfo.color}`}>{overallRiskScore}/10</p>
                  <Badge className={`${riskInfo.bgColor} ${riskInfo.color} ${riskInfo.borderColor} text-xs mt-1`}>
                    {riskInfo.level}
                  </Badge>
                </div>
                <Shield className={`w-6 h-6 ${riskInfo.color}`} />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/fraud">
          <Card className="bg-gray-900 border-gray-800 hover:border-red-400/50 transition-colors cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-400">SIMbox Risk</p>
                  <p className="text-xl font-bold text-red-400">
                    {fraudData.calls?.fraudIndicators?.shortCallsPercentage || 0}%
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {fraudData.calls?.fraudIndicators?.shortCallsCount || 0} short calls
                  </p>
                </div>
                <Target className="w-6 h-6 text-red-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/fraud">
          <Card className="bg-gray-900 border-gray-800 hover:border-orange-400/50 transition-colors cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-400">IMEI Changes</p>
                  <p className="text-xl font-bold text-orange-400">
                    {fraudData.imei?.fraudIndicators?.totalIMEICount || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {fraudData.imei?.fraudIndicators?.imeiSwitchFrequency || 'NORMAL'}
                  </p>
                </div>
                <Smartphone className="w-6 h-6 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/calls">
          <Card className="bg-gray-900 border-gray-800 hover:border-blue-400/50 transition-colors cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-400">Call Volume</p>
                  <p className="text-xl font-bold text-blue-400">
                    {(fraudData.calls?.totalCalls || 0) + (data.internationalCall?.totalCalls || 0)}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Max: {fraudData.calls?.fraudIndicators?.maxCallsInHour || 0}/hr
                  </p>
                </div>
                <Phone className="w-6 h-6 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/fraud">
          <Card className="bg-gray-900 border-gray-800 hover:border-yellow-400/50 transition-colors cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-400">No Incoming</p>
                  <p className="text-xl font-bold text-yellow-400">
                    {fraudData.calls?.fraudIndicators?.noIncomingCallsPeriods || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    I/O: {fraudData.calls?.ratioIncomingOutgoing || '0%'}
                  </p>
                </div>
                <Bell className="w-6 h-6 text-yellow-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/fraud">
          <Card className="bg-gray-900 border-gray-800 hover:border-purple-400/50 transition-colors cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-400">Location Risk</p>
                  <p className="text-xl font-bold text-purple-400">
                    {fraudData.calls?.fraudIndicators?.geographicMovement?.cellTowerSwitches || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {fraudData.calls?.fraudIndicators?.geographicMovement?.maxSpeedKmh || 0} km/h
                  </p>
                </div>
                <MapPin className="w-6 h-6 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Fraud Analytics & Alerts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Fraud Analysis */}
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-white">
              <Target className="w-5 h-5 mr-2" />
              Quick Fraud Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400 text-sm">Call Pattern Risk</span>
                <span className="font-mono text-yellow-400">{fraudData.calls?.riskScore || 0}/10</span>
              </div>
              <Progress value={(fraudData.calls?.riskScore || 0) * 10} className="h-2" />

              <div className="flex justify-between items-center">
                <span className="text-gray-400 text-sm">Device Risk (IMEI)</span>
                <span className="font-mono text-orange-400">{fraudData.imei?.riskScore || 0}/10</span>
              </div>
              <Progress value={(fraudData.imei?.riskScore || 0) * 10} className="h-2" />

              <div className="flex justify-between items-center">
                <span className="text-gray-400 text-sm">Subscriber Profile Risk</span>
                <span className="font-mono text-blue-400">{fraudData.subscriber?.fraudProfile?.overallRiskScore || 0}/10</span>
              </div>
              <Progress value={(fraudData.subscriber?.fraudProfile?.overallRiskScore || 0) * 10} className="h-2" />
            </div>

            <div className="mt-4 p-3 bg-purple-900/10 rounded-lg border border-purple-400/20">
              <p className="text-purple-400 text-sm font-medium">Risk Assessment</p>
              <p className="text-purple-300 text-xs mt-1">
                {overallRiskScore >= 7 ?
                  "High fraud probability detected. Immediate investigation recommended." :
                  overallRiskScore >= 4 ?
                  "Medium risk detected. Monitor closely for additional patterns." :
                  "Low risk profile. Continue routine monitoring."
                }
              </p>
            </div>

            <div className="flex space-x-2 mt-4">
              <Link href="/fraud" className="flex-1">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  <Eye className="w-4 h-4 mr-1" />
                  Investigate
                </Button>
              </Link>
              <Link href="/fraud-analytics" className="flex-1">
                <Button variant="outline" className="w-full border-gray-600">
                  <BarChart3 className="w-4 h-4 mr-1" />
                  Analytics
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

      {/* Quick Overview Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-400">
              <Phone className="w-5 h-5 mr-2" />
              Call Activity Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Local Calls</span>
              <span className="font-mono text-blue-400">{data.localCall?.totalCalls}</span>
            </div>
            <Progress
              value={
                ((data.localCall?.totalCalls || 0) /
                  ((data.localCall?.totalCalls || 0) + (data.internationalCall?.totalCalls || 0))) *
                100
              }
              className="h-2"
            />

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">International Calls</span>
              <span className="font-mono text-green-400">{data.internationalCall?.totalCalls}</span>
            </div>
            <Progress
              value={
                ((data.internationalCall?.totalCalls || 0) /
                  ((data.localCall?.totalCalls || 0) + (data.internationalCall?.totalCalls || 0))) *
                100
              }
              className="h-2"
            />

            <div className="flex justify-between items-center pt-2">
              <span className="text-sm text-gray-400">Peak Hour</span>
              <span className="font-mono text-yellow-400">{data.localCall?.peakHour}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-cyan-400">
              <Radio className="w-5 h-5 mr-2" />
              Network Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Active Cell Sites</span>
              <span className="font-mono text-cyan-400">{data.network?.uniqueCellSites}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Total Network Calls</span>
              <span className="font-mono text-cyan-400">{data.network?.totalCalls}</span>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-gray-400">Cell Distribution</p>
              {data.network?.cells?.slice(0, 2).map((cell: any, index: number) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="font-mono text-xs text-gray-500">{cell.cellId}</span>
                  <span className="font-mono text-xs text-cyan-400">{cell.totalCalls} calls</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Alerts */}
      <RealTimeAlerts />
    </div>
  )
}
