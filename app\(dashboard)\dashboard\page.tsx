"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Phone,
  MessageSquare,
  Radio,
  CreditCard,
  Users,
  Download,
  TrendingUp,
  TrendingDown,
  Activity,
} from "lucide-react"
import Link from "next/link"

export default function Dashboard() {
  const [data, setData] = useState<any>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [subscriberRes, rechargeRes, dealerRes, networkRes, localCallRes, internationalCallRes, smsRes, dataRes] =
          await Promise.all([
            fetch("/api/subscriber-profile.json"),
            fetch("/api/recharge-payment.json"),
            fetch("/api/dealer-activations.json"),
            fetch("/api/network-usage.json"),
            fetch("/api/local-call-activity.json"),
            fetch("/api/international-call-activity.json"),
            fetch("/api/sms-activity.json"),
            fetch("/api/data-activity.json"),
          ])

        const [
          subscriberData,
          rechargeData,
          dealerData,
          networkData,
          localCallData,
          internationalCallData,
          smsData,
          dataData,
        ] = await Promise.all([
          subscriberRes.json(),
          rechargeRes.json(),
          dealerRes.json(),
          networkRes.json(),
          localCallRes.json(),
          internationalCallRes.json(),
          smsRes.json(),
          dataRes.json(),
        ])

        setData({
          subscriber: subscriberData,
          recharge: rechargeData,
          dealer: dealerData,
          network: networkData,
          localCall: localCallData,
          internationalCall: internationalCallData,
          sms: smsData,
          dataActivity: dataData,
        })
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Welcome to TelecomPro
          </h1>
          <p className="text-gray-400 mt-2">Comprehensive analytics for subscriber {data.subscriber?.msisdn}</p>
        </div>
        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-400/20">
            <Activity className="w-4 h-4 mr-1" />
            Live Data
          </Badge>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link href="/recharge">
          <Card className="bg-gray-900 border-gray-800 hover:border-green-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Revenue</p>
                  <p className="text-2xl font-bold text-green-400">DH {data.recharge?.totalValueDH}</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                    <p className="text-sm text-green-400">+12.5%</p>
                  </div>
                </div>
                <CreditCard className="w-8 h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/calls">
          <Card className="bg-gray-900 border-gray-800 hover:border-blue-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Calls</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {(data.localCall?.totalCalls || 0) + (data.internationalCall?.totalCalls || 0)}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-blue-400 mr-1" />
                    <p className="text-sm text-blue-400">****%</p>
                  </div>
                </div>
                <Phone className="w-8 h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/sms">
          <Card className="bg-gray-900 border-gray-800 hover:border-purple-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">SMS Messages</p>
                  <p className="text-2xl font-bold text-purple-400">{data.sms?.totalSMS}</p>
                  <div className="flex items-center mt-1">
                    <TrendingDown className="w-4 h-4 text-red-400 mr-1" />
                    <p className="text-sm text-red-400">-3.1%</p>
                  </div>
                </div>
                <MessageSquare className="w-8 h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/data">
          <Card className="bg-gray-900 border-gray-800 hover:border-orange-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Data Usage</p>
                  <p className="text-2xl font-bold text-orange-400">
                    {data.dataActivity?.downloadMB + data.dataActivity?.uploadMB} MB
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-orange-400 mr-1" />
                    <p className="text-sm text-orange-400">+15.7%</p>
                  </div>
                </div>
                <Download className="w-8 h-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Quick Overview Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-400">
              <Phone className="w-5 h-5 mr-2" />
              Call Activity Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Local Calls</span>
              <span className="font-mono text-blue-400">{data.localCall?.totalCalls}</span>
            </div>
            <Progress
              value={
                ((data.localCall?.totalCalls || 0) /
                  ((data.localCall?.totalCalls || 0) + (data.internationalCall?.totalCalls || 0))) *
                100
              }
              className="h-2"
            />

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">International Calls</span>
              <span className="font-mono text-green-400">{data.internationalCall?.totalCalls}</span>
            </div>
            <Progress
              value={
                ((data.internationalCall?.totalCalls || 0) /
                  ((data.localCall?.totalCalls || 0) + (data.internationalCall?.totalCalls || 0))) *
                100
              }
              className="h-2"
            />

            <div className="flex justify-between items-center pt-2">
              <span className="text-sm text-gray-400">Peak Hour</span>
              <span className="font-mono text-yellow-400">{data.localCall?.peakHour}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-cyan-400">
              <Radio className="w-5 h-5 mr-2" />
              Network Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Active Cell Sites</span>
              <span className="font-mono text-cyan-400">{data.network?.uniqueCellSites}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Total Network Calls</span>
              <span className="font-mono text-cyan-400">{data.network?.totalCalls}</span>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-gray-400">Cell Distribution</p>
              {data.network?.cells?.slice(0, 2).map((cell: any, index: number) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="font-mono text-xs text-gray-500">{cell.cellId}</span>
                  <span className="font-mono text-xs text-cyan-400">{cell.totalCalls} calls</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link href="/subscriber">
          <Card className="bg-gray-900 border-gray-800 hover:border-blue-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6 text-center">
              <Users className="w-12 h-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Subscriber Details</h3>
              <p className="text-gray-400 text-sm">View complete subscriber profile and account information</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/dealers">
          <Card className="bg-gray-900 border-gray-800 hover:border-yellow-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6 text-center">
              <Users className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Dealer Management</h3>
              <p className="text-gray-400 text-sm">Monitor dealer activations and performance metrics</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/settings">
          <Card className="bg-gray-900 border-gray-800 hover:border-gray-500/50 transition-colors cursor-pointer">
            <CardContent className="p-6 text-center">
              <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Platform Settings</h3>
              <p className="text-gray-400 text-sm">Configure alerts, reports, and system preferences</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
