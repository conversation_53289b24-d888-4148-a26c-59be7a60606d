"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area"
import {
  <PERSON>ert<PERSON><PERSON>gle,
  Bell,
  Clock,
  Eye,
  X,
  CheckCircle,
  Shield,
  Target,
  Smartphone,
  MapPin,
  Users,
  Zap,
} from "lucide-react"

export interface FraudAlert {
  id: string
  type: 'SIMBOX' | 'IMEI_SWITCHING' | 'GEOGRAPHIC_ANOMALY' | 'HIGH_VOLUME' | 'PATTERN_ANOMALY'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  msisdn: string
  message: string
  timestamp: Date
  riskScore: number
  location?: string
  autoActions: string[]
  status: 'NEW' | 'ACKNOWLEDGED' | 'INVESTIGATING' | 'RESOLVED' | 'FALSE_POSITIVE'
}

const mockAlerts: <PERSON><PERSON><PERSON><PERSON>t[] = [
  {
    id: "ALT-001",
    type: "SIMBOX",
    severity: "CRITICAL",
    msisdn: "+212333336",
    message: "SIMbox fraud detected: 85% short calls, no incoming traffic",
    timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
    riskScore: 9.2,
    location: "Casablanca",
    autoActions: ["BLOCK_OUTGOING", "ALERT_SECURITY"],
    status: "NEW"
  },
  {
    id: "ALT-002", 
    type: "IMEI_SWITCHING",
    severity: "HIGH",
    msisdn: "+212444447",
    message: "Rapid IMEI switching: 5 devices in 24 hours",
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    riskScore: 8.1,
    location: "Rabat",
    autoActions: ["MONITOR_CLOSELY"],
    status: "NEW"
  },
  {
    id: "ALT-003",
    type: "HIGH_VOLUME",
    severity: "MEDIUM",
    msisdn: "+212555558",
    message: "Unusual call volume: 150 calls in 1 hour",
    timestamp: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
    riskScore: 6.8,
    location: "Marrakech",
    autoActions: ["RATE_LIMIT"],
    status: "ACKNOWLEDGED"
  },
  {
    id: "ALT-004",
    type: "GEOGRAPHIC_ANOMALY",
    severity: "HIGH",
    msisdn: "+212666669",
    message: "Impossible movement: 200km in 30 minutes",
    timestamp: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago
    riskScore: 7.5,
    location: "Fez → Tangier",
    autoActions: ["VERIFY_LOCATION"],
    status: "INVESTIGATING"
  }
]

export function RealTimeAlerts() {
  const [alerts, setAlerts] = useState<FraudAlert[]>(mockAlerts)
  const [filter, setFilter] = useState<'ALL' | 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW'>('ALL')
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Simulate real-time updates
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      // Simulate new alerts occasionally
      if (Math.random() < 0.1) { // 10% chance every 5 seconds
        const newAlert: FraudAlert = {
          id: `ALT-${Date.now()}`,
          type: ['SIMBOX', 'IMEI_SWITCHING', 'HIGH_VOLUME', 'GEOGRAPHIC_ANOMALY'][Math.floor(Math.random() * 4)] as any,
          severity: ['CRITICAL', 'HIGH', 'MEDIUM'][Math.floor(Math.random() * 3)] as any,
          msisdn: `+21277777${Math.floor(Math.random() * 10)}`,
          message: "New fraud pattern detected",
          timestamp: new Date(),
          riskScore: 5 + Math.random() * 5,
          location: ["Casablanca", "Rabat", "Marrakech", "Fez"][Math.floor(Math.random() * 4)],
          autoActions: ["MONITOR_CLOSELY"],
          status: "NEW"
        }
        setAlerts(prev => [newAlert, ...prev])
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [autoRefresh])

  const filteredAlerts = alerts.filter(alert => 
    filter === 'ALL' || alert.severity === filter
  )

  const getAlertIcon = (type: string) => {
    switch(type) {
      case 'SIMBOX': return Target
      case 'IMEI_SWITCHING': return Smartphone
      case 'GEOGRAPHIC_ANOMALY': return MapPin
      case 'HIGH_VOLUME': return Zap
      default: return AlertTriangle
    }
  }

  const getSeverityColor = (severity: string) => {
    switch(severity) {
      case 'CRITICAL': return 'text-red-400 bg-red-900/20 border-red-400/20'
      case 'HIGH': return 'text-orange-400 bg-orange-900/20 border-orange-400/20'
      case 'MEDIUM': return 'text-yellow-400 bg-yellow-900/20 border-yellow-400/20'
      default: return 'text-green-400 bg-green-900/20 border-green-400/20'
    }
  }

  const getStatusColor = (status: string) => {
    switch(status) {
      case 'NEW': return 'text-red-400'
      case 'ACKNOWLEDGED': return 'text-yellow-400'
      case 'INVESTIGATING': return 'text-blue-400'
      case 'RESOLVED': return 'text-green-400'
      case 'FALSE_POSITIVE': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  const updateAlertStatus = (alertId: string, newStatus: FraudAlert['status']) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, status: newStatus } : alert
    ))
  }

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }

  const formatTimeAgo = (timestamp: Date) => {
    const minutes = Math.floor((Date.now() - timestamp.getTime()) / (1000 * 60))
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    return `${hours}h ago`
  }

  const criticalCount = alerts.filter(a => a.severity === 'CRITICAL' && a.status === 'NEW').length
  const highCount = alerts.filter(a => a.severity === 'HIGH' && a.status === 'NEW').length

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center text-white">
            <Bell className="w-5 h-5 mr-2" />
            Real-time Fraud Alerts
            {(criticalCount > 0 || highCount > 0) && (
              <Badge className="ml-2 bg-red-900/20 text-red-400 border-red-400/20">
                {criticalCount + highCount} urgent
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={autoRefresh ? 'border-green-400/20 text-green-400' : 'border-gray-600 text-gray-400'}
            >
              {autoRefresh ? 'Live' : 'Paused'}
            </Button>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm text-white"
            >
              <option value="ALL">All Alerts</option>
              <option value="CRITICAL">Critical</option>
              <option value="HIGH">High</option>
              <option value="MEDIUM">Medium</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {filteredAlerts.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <Shield className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No alerts matching current filter</p>
              </div>
            ) : (
              filteredAlerts.map((alert) => {
                const AlertIcon = getAlertIcon(alert.type)
                return (
                  <div
                    key={alert.id}
                    className={`p-4 rounded-lg border transition-all ${
                      alert.severity === 'CRITICAL' 
                        ? 'bg-red-900/10 border-red-400/20' 
                        : alert.severity === 'HIGH'
                        ? 'bg-orange-900/10 border-orange-400/20'
                        : 'bg-gray-800/50 border-gray-700'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <AlertIcon className={`w-5 h-5 mt-0.5 ${
                          alert.severity === 'CRITICAL' ? 'text-red-400' :
                          alert.severity === 'HIGH' ? 'text-orange-400' :
                          'text-yellow-400'
                        }`} />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <Badge className={getSeverityColor(alert.severity)}>
                              {alert.severity}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {alert.type.replace('_', ' ')}
                            </Badge>
                            <span className={`text-xs ${getStatusColor(alert.status)}`}>
                              {alert.status}
                            </span>
                          </div>
                          <p className="text-white font-medium">{alert.msisdn}</p>
                          <p className="text-gray-300 text-sm">{alert.message}</p>
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                            <span className="flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {formatTimeAgo(alert.timestamp)}
                            </span>
                            <span className="flex items-center">
                              <Shield className="w-3 h-3 mr-1" />
                              Risk: {alert.riskScore.toFixed(1)}/10
                            </span>
                            {alert.location && (
                              <span className="flex items-center">
                                <MapPin className="w-3 h-3 mr-1" />
                                {alert.location}
                              </span>
                            )}
                          </div>
                          {alert.autoActions.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs text-gray-400 mb-1">Auto Actions:</p>
                              <div className="flex flex-wrap gap-1">
                                {alert.autoActions.map((action, idx) => (
                                  <Badge key={idx} variant="outline" className="text-xs">
                                    {action.replace('_', ' ')}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        {alert.status === 'NEW' && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateAlertStatus(alert.id, 'ACKNOWLEDGED')}
                              className="h-8 px-2 border-blue-400/20 text-blue-400"
                            >
                              <Eye className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateAlertStatus(alert.id, 'INVESTIGATING')}
                              className="h-8 px-2 border-yellow-400/20 text-yellow-400"
                            >
                              Investigate
                            </Button>
                          </>
                        )}
                        {alert.status === 'ACKNOWLEDGED' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateAlertStatus(alert.id, 'INVESTIGATING')}
                            className="h-8 px-2 border-yellow-400/20 text-yellow-400"
                          >
                            Investigate
                          </Button>
                        )}
                        {alert.status === 'INVESTIGATING' && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateAlertStatus(alert.id, 'RESOLVED')}
                              className="h-8 px-2 border-green-400/20 text-green-400"
                            >
                              <CheckCircle className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateAlertStatus(alert.id, 'FALSE_POSITIVE')}
                              className="h-8 px-2 border-gray-400/20 text-gray-400"
                            >
                              False +
                            </Button>
                          </>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => dismissAlert(alert.id)}
                          className="h-8 w-8 p-0 border-gray-600 text-gray-400"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

export function AlertSummary() {
  const [alerts] = useState<FraudAlert[]>(mockAlerts)
  
  const criticalCount = alerts.filter(a => a.severity === 'CRITICAL' && a.status === 'NEW').length
  const highCount = alerts.filter(a => a.severity === 'HIGH' && a.status === 'NEW').length
  const mediumCount = alerts.filter(a => a.severity === 'MEDIUM' && a.status === 'NEW').length
  const totalActive = criticalCount + highCount + mediumCount

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card className="bg-gray-900 border-red-400/20">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Critical Alerts</p>
              <p className="text-2xl font-bold text-red-400">{criticalCount}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-400" />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900 border-orange-400/20">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">High Priority</p>
              <p className="text-2xl font-bold text-orange-400">{highCount}</p>
            </div>
            <Bell className="w-8 h-8 text-orange-400" />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900 border-yellow-400/20">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Medium Priority</p>
              <p className="text-2xl font-bold text-yellow-400">{mediumCount}</p>
            </div>
            <Eye className="w-8 h-8 text-yellow-400" />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900 border-gray-800">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Active</p>
              <p className="text-2xl font-bold text-blue-400">{totalActive}</p>
            </div>
            <Shield className="w-8 h-8 text-blue-400" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
