"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { BarChart3 } from "lucide-react"

interface CallActivityChartProps {
  localCalls: number
  internationalCalls: number
}

export function CallActivityChart({ localCalls, internationalCalls }: CallActivityChartProps) {
  const total = localCalls + internationalCalls
  const localPercentage = (localCalls / total) * 100
  const internationalPercentage = (internationalCalls / total) * 100

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="flex items-center text-blue-400">
          <BarChart3 className="w-5 h-5 mr-2" />
          Call Distribution Chart
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="relative h-64 flex items-end justify-center space-x-4">
            <div className="flex flex-col items-center">
              <div
                className="w-16 bg-blue-500 rounded-t-lg transition-all duration-500"
                style={{ height: `${(localPercentage / 100) * 200}px` }}
              />
              <p className="text-sm text-gray-400 mt-2">Local</p>
              <p className="text-lg font-bold text-blue-400">{localCalls}</p>
            </div>
            <div className="flex flex-col items-center">
              <div
                className="w-16 bg-green-500 rounded-t-lg transition-all duration-500"
                style={{ height: `${(internationalPercentage / 100) * 200}px` }}
              />
              <p className="text-sm text-gray-400 mt-2">International</p>
              <p className="text-lg font-bold text-green-400">{internationalCalls}</p>
            </div>
          </div>
          <div className="text-center">
            <p className="text-gray-400">
              Total Calls: <span className="text-white font-bold">{total}</span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
