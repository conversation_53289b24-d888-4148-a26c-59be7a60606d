"use client"

import { useEffect, useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { BarChart3, Download, Upload, Radio, TrendingUp, Wifi, Activity, RefreshCw, HardDrive, Zap } from "lucide-react"
import { DataUsageChart } from "@/components/charts/data-usage-chart"

export default function DataPage() {
  const [dataActivity, setDataActivity] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  const fetchData = async () => {
    try {
      const response = await fetch("/api/data-activity.json")
      const data = await response.json()
      setDataActivity(data)
      setLastUpdate(new Date())
    } catch (error) {
      console.error("Error fetching data activity:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()

    // Real-time updates every 20 seconds
    const interval = setInterval(fetchData, 20000)
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  const totalData = (dataActivity?.downloadMB || 0) + (dataActivity?.uploadMB || 0)
  const downloadPercentage = ((dataActivity?.downloadMB || 0) / totalData) * 100
  const uploadPercentage = ((dataActivity?.uploadMB || 0) / totalData) * 100

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Data Analytics</h1>
          <p className="text-gray-400 mt-2">Bandwidth monitoring and usage optimization insights</p>
          <p className="text-xs text-gray-500 mt-1">Last updated: {lastUpdate.toLocaleTimeString()}</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm" onClick={fetchData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <HardDrive className="w-4 h-4 mr-2" />
            Export Data
          </Button>
          <Badge variant="secondary" className="bg-orange-900/20 text-orange-400 border-orange-400/20">
            <Activity className="w-4 h-4 mr-1" />
            Real-time
          </Badge>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Data Usage</p>
                <p className="text-2xl font-bold text-orange-400">{totalData} MB</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <p className="text-sm text-green-400">+15.7%</p>
                </div>
              </div>
              <BarChart3 className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Download</p>
                <p className="text-2xl font-bold text-green-400">{dataActivity?.downloadMB} MB</p>
                <p className="text-sm text-gray-400">{downloadPercentage.toFixed(1)}% of total</p>
              </div>
              <Download className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Upload</p>
                <p className="text-2xl font-bold text-blue-400">{dataActivity?.uploadMB} MB</p>
                <p className="text-sm text-gray-400">{uploadPercentage.toFixed(1)}% of total</p>
              </div>
              <Upload className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active Cell Towers</p>
                <p className="text-2xl font-bold text-cyan-400">{dataActivity?.distinctCellIds}</p>
                <p className="text-sm text-gray-400">Data sources</p>
              </div>
              <Radio className="w-8 h-8 text-cyan-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Usage Chart */}
      <DataUsageChart
        downloadMB={dataActivity?.downloadMB || 0}
        uploadMB={dataActivity?.uploadMB || 0}
        breakdown={dataActivity?.breakdown || []}
      />

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-gray-900 border border-gray-800">
          <TabsTrigger value="overview" className="data-[state=active]:bg-orange-600">
            Usage Overview
          </TabsTrigger>
          <TabsTrigger value="cells" className="data-[state=active]:bg-cyan-600">
            Cell Tower Analysis
          </TabsTrigger>
          <TabsTrigger value="optimization" className="data-[state=active]:bg-purple-600">
            Optimization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-green-400">
                  <Download className="w-5 h-5 mr-2" />
                  Download Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <p className="text-4xl font-bold text-green-400 mb-2">{dataActivity?.downloadMB} MB</p>
                  <p className="text-gray-400">Total Downloaded</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Peak Download Hour</span>
                    <Badge className="bg-green-900/20 text-green-400 border-green-400/20">2-3 PM</Badge>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Average Speed</span>
                    <span className="font-mono text-green-400">2.5 Mbps</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Efficiency Rate</span>
                    <Badge className="bg-green-900/20 text-green-400 border-green-400/20">94.2%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-400">
                  <Upload className="w-5 h-5 mr-2" />
                  Upload Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <p className="text-4xl font-bold text-blue-400 mb-2">{dataActivity?.uploadMB} MB</p>
                  <p className="text-gray-400">Total Uploaded</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Peak Upload Hour</span>
                    <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">10-11 AM</Badge>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Average Speed</span>
                    <span className="font-mono text-blue-400">1.8 Mbps</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Success Rate</span>
                    <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">98.1%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Data Distribution */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-purple-400">Data Distribution Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Download Traffic</span>
                <span className="font-mono text-green-400">
                  {dataActivity?.downloadMB} MB ({downloadPercentage.toFixed(1)}%)
                </span>
              </div>
              <Progress value={downloadPercentage} className="h-3" />

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Upload Traffic</span>
                <span className="font-mono text-blue-400">
                  {dataActivity?.uploadMB} MB ({uploadPercentage.toFixed(1)}%)
                </span>
              </div>
              <Progress value={uploadPercentage} className="h-3" />

              <div className="pt-4 border-t border-gray-800">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Download/Upload Ratio</span>
                  <span className="font-mono text-orange-400">
                    {(dataActivity?.downloadMB / dataActivity?.uploadMB).toFixed(1)}:1
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cells" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {dataActivity?.breakdown?.map((cell: any, index: number) => (
              <Card key={index} className="bg-gray-900 border-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-cyan-400">
                    <Radio className="w-5 h-5 mr-2" />
                    Cell Tower {cell.cellId}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-400">{cell.downloadMB}</p>
                      <p className="text-sm text-gray-400">Download MB</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-400">{cell.uploadMB}</p>
                      <p className="text-sm text-gray-400">Upload MB</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Total Usage</span>
                      <span className="font-mono text-orange-400">{cell.downloadMB + cell.uploadMB} MB</span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Load Distribution</span>
                      <span className="font-mono text-purple-400">
                        {(((cell.downloadMB + cell.uploadMB) / totalData) * 100).toFixed(1)}%
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Performance</span>
                      <Badge
                        className={
                          cell.downloadMB + cell.uploadMB > 20
                            ? "bg-green-900/20 text-green-400 border-green-400/20"
                            : "bg-yellow-900/20 text-yellow-400 border-yellow-400/20"
                        }
                      >
                        {cell.downloadMB + cell.uploadMB > 20 ? "High" : "Moderate"}
                      </Badge>
                    </div>
                  </div>

                  <Progress value={((cell.downloadMB + cell.uploadMB) / totalData) * 100} className="h-2" />
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-yellow-400">
                  <Zap className="w-5 h-5 mr-2" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Network Efficiency</span>
                  <Badge className="bg-green-900/20 text-green-400 border-green-400/20">96.3%</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Bandwidth Utilization</span>
                  <span className="font-mono text-yellow-400">78%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Peak Load Capacity</span>
                  <span className="font-mono text-orange-400">85%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Latency Average</span>
                  <span className="font-mono text-cyan-400">45ms</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Error Rate</span>
                  <span className="font-mono text-red-400">0.2%</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-purple-400">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Usage Trends
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Weekly Growth</span>
                  <div className="flex items-center">
                    <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                    <span className="font-mono text-green-400">+15.7%</span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Peak Usage Day</span>
                  <Badge className="bg-purple-900/20 text-purple-400 border-purple-400/20">Friday</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Off-peak Savings</span>
                  <span className="font-mono text-green-400">23%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Projected Monthly</span>
                  <span className="font-mono text-purple-400">{(totalData * 4.3).toFixed(0)} MB</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Optimization Score</span>
                  <Badge className="bg-green-900/20 text-green-400 border-green-400/20">8.7/10</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Optimization Recommendations */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-orange-400">Optimization Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2" />
                  <div>
                    <p className="text-white font-medium">Load Balancing</p>
                    <p className="text-gray-400 text-sm">
                      Cell {dataActivity?.breakdown?.[0]?.cellId} is handling{" "}
                      {dataActivity?.breakdown?.[0]?.downloadMB + dataActivity?.breakdown?.[0]?.uploadMB}MB. Consider
                      redistributing traffic for optimal performance.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2" />
                  <div>
                    <p className="text-white font-medium">Bandwidth Optimization</p>
                    <p className="text-gray-400 text-sm">
                      Current download/upload ratio is {(dataActivity?.downloadMB / dataActivity?.uploadMB).toFixed(1)}
                      :1. Optimize caching for better efficiency.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2" />
                  <div>
                    <p className="text-white font-medium">Capacity Planning</p>
                    <p className="text-gray-400 text-sm">
                      Based on current growth trends, consider upgrading capacity by Q2 to handle projected{" "}
                      {(totalData * 4.3 * 3).toFixed(0)}MB quarterly usage.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-gray-400">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start bg-transparent">
              <BarChart3 className="w-4 h-4 mr-2" />
              Usage Report
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Wifi className="w-4 h-4 mr-2" />
              Network Health
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Zap className="w-4 h-4 mr-2" />
              Optimize Performance
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <HardDrive className="w-4 h-4 mr-2" />
              Export Analytics
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
