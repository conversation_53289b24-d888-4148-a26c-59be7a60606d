-- DML: Insert SMS activity data
INSERT INTO sms_activity (timestamp, caller_number, called_number, direction, scope) VALUES
('2024-07-17 10:00:00', '9876543210', '9123456781', 'Outgoing', 'Local'),
('2024-07-17 10:05:00', '9876543210', '9123456782', 'Outgoing', 'Local'),
('2024-07-17 10:10:00', '9876543210', '9123456783', 'Outgoing', 'Local'),
('2024-07-17 10:15:00', '9876543210', '9123456781', 'Outgoing', 'Local'),
('2024-07-17 10:20:00', '9876543210', '9123456782', 'Outgoing', 'Local'),
('2024-07-17 10:25:00', '9876543210', '9123456784', 'Outgoing', 'Local'),
('2024-07-17 10:30:00', '9876543210', '9123456785', 'Outgoing', 'Local'),
('2024-07-17 10:35:00', '9123456786', '9876543210', 'Incoming', 'Local'),
('2024-07-17 10:40:00', '9123456786', '9876543210', 'Incoming', 'Local'),
('2024-07-17 10:45:00', '9123456787', '9876543210', 'Incoming', 'Local');
