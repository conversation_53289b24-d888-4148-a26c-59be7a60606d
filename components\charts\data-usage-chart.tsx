"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Download, Upload } from "lucide-react"

interface DataUsageChartProps {
  downloadMB: number
  uploadMB: number
  breakdown: Array<{ cellId: string; downloadMB: number; uploadMB: number }>
}

export function DataUsageChart({ downloadMB, uploadMB, breakdown }: DataUsageChartProps) {
  const total = downloadMB + uploadMB

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="text-orange-400">Data Usage Visualization</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Usage */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Download className="w-4 h-4 text-green-400" />
              <span className="text-gray-400">Download</span>
            </div>
            <span className="font-mono text-green-400">{downloadMB} MB</span>
          </div>
          <Progress value={(downloadMB / total) * 100} className="h-3" />

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Upload className="w-4 h-4 text-blue-400" />
              <span className="text-gray-400">Upload</span>
            </div>
            <span className="font-mono text-blue-400">{uploadMB} MB</span>
          </div>
          <Progress value={(uploadMB / total) * 100} className="h-3" />
        </div>

        {/* Cell Breakdown */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-300">Usage by Cell Tower</h4>
          {breakdown.map((cell, index) => {
            const cellTotal = cell.downloadMB + cell.uploadMB
            return (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">{cell.cellId}</span>
                  <span className="text-sm font-mono text-orange-400">{cellTotal} MB</span>
                </div>
                <div className="flex space-x-1">
                  <div
                    className="h-2 bg-green-500 rounded-l"
                    style={{ width: `${(cell.downloadMB / cellTotal) * 100}%` }}
                  />
                  <div
                    className="h-2 bg-blue-500 rounded-r"
                    style={{ width: `${(cell.uploadMB / cellTotal) * 100}%` }}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
