"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { User, Phone, MapPin, Calendar, CreditCard, Activity, Edit, Download, RefreshCw } from "lucide-react"

export default function SubscriberPage() {
  const [subscriber, setSubscriber] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/subscriber-profile.json")
        const data = await response.json()
        setSubscriber(data)
      } catch (error) {
        console.error("Error fetching subscriber data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Subscriber Profile</h1>
          <p className="text-gray-400 mt-2">Complete subscriber information and account details</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Edit Profile
          </Button>
        </div>
      </div>

      {/* Main Profile Card */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center text-blue-400">
              <User className="w-6 h-6 mr-2" />
              Primary Information
            </CardTitle>
            <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-400/20">
              <Activity className="w-4 h-4 mr-1" />
              Active
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center text-gray-400">
                <Phone className="w-4 h-4 mr-2" />
                <span className="text-sm">MSISDN</span>
              </div>
              <p className="font-mono text-xl text-white">{subscriber?.msisdn}</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-gray-400">
                <CreditCard className="w-4 h-4 mr-2" />
                <span className="text-sm">Line Type</span>
              </div>
              <Badge variant="outline" className="text-blue-400 border-blue-400/20">
                {subscriber?.lineType}
              </Badge>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-gray-400">
                <MapPin className="w-4 h-4 mr-2" />
                <span className="text-sm">City</span>
              </div>
              <p className="text-white font-medium">{subscriber?.city}</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-gray-400">
                <Calendar className="w-4 h-4 mr-2" />
                <span className="text-sm">Activation Date</span>
              </div>
              <p className="text-white">{subscriber?.activationDate}</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-gray-400">
                <Calendar className="w-4 h-4 mr-2" />
                <span className="text-sm">Last Modified</span>
              </div>
              <p className="text-white">{subscriber?.lastModifiedDate}</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-gray-400">
                <User className="w-4 h-4 mr-2" />
                <span className="text-sm">Dealer Code</span>
              </div>
              <p className="font-mono text-white">{subscriber?.dealerCode}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-purple-400">Account Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Date of Birth</span>
              <span className="text-white">{subscriber?.dob || "Not provided"}</span>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Subscription Plan</span>
              <span className="text-white">{subscriber?.subscriptionPlan || "Not specified"}</span>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">HLR Status</span>
              <Badge variant={subscriber?.hlrStatus ? "default" : "secondary"}>
                {subscriber?.hlrStatus || "Unknown"}
              </Badge>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">CIN</span>
              <span className="font-mono text-white">{subscriber?.cin || "Not provided"}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-green-400">Account Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Account Status</span>
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">Active</Badge>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex items-center justify-between">
              <span className="text-gray-400">Service Status</span>
              <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">Operational</Badge>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex items-center justify-between">
              <span className="text-gray-400">Last Activity</span>
              <span className="text-white">2 hours ago</span>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex items-center justify-between">
              <span className="text-gray-400">Account Age</span>
              <span className="text-white">2 months</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-yellow-400">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start bg-transparent">
              <Phone className="w-4 h-4 mr-2" />
              View Call History
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <CreditCard className="w-4 h-4 mr-2" />
              Billing History
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Activity className="w-4 h-4 mr-2" />
              Usage Analytics
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Edit className="w-4 h-4 mr-2" />
              Update Profile
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
