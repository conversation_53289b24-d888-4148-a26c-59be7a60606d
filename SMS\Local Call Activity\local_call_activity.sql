-- DDL: Create local_call_activity table
CREATE TABLE local_call_activity (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    caller_number VARCHAR(20),
    called_number VARCHAR(20),
    call_direction ENUM('Outgoing', 'Incoming'),
    duration INT, -- duration in seconds or minutes
    cell_id VARCHAR(50),
    UNIQUE KEY (timestamp, caller_number, called_number, call_direction)
);
