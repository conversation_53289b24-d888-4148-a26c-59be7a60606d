-- DDL: Create international_call_activity table
CREATE TABLE international_call_activity (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    caller_number VARCHAR(20),
    called_number VARCHAR(20),
    call_direction ENUM('Outgoing', 'Incoming'),
    duration INT, -- in seconds or minutes
    country_name VARCHAR(100),
    UNIQUE KEY (timestamp, caller_number, called_number, call_direction)
);
