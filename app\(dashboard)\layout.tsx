import type React from "react"
import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex h-screen bg-gray-950">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-y-auto bg-gray-950">{children}</main>
      </div>
    </div>
  )
}
