"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  Phone,
  MessageSquare,
  Radio,
  Globe,
  CreditCard,
  Users,
  Activity,
  Zap,
  Shield,
  Clock,
  Database,
  ArrowRight,
  CheckCircle,
  Star,
  Play,
} from "lucide-react"

const features = [
  {
    icon: BarChart3,
    title: "Real-time Analytics",
    description: "Live data streaming with automatic updates every 20-45 seconds",
    color: "text-blue-400",
    bgColor: "bg-blue-900/20",
  },
  {
    icon: Phone,
    title: "Call Activity Monitoring",
    description: "Comprehensive local and international call pattern analysis",
    color: "text-green-400",
    bgColor: "bg-green-900/20",
  },
  {
    icon: MessageSquare,
    title: "SMS Analytics",
    description: "Text messaging patterns with delivery and response tracking",
    color: "text-purple-400",
    bgColor: "bg-purple-900/20",
  },
  {
    icon: Radio,
    title: "Network Performance",
    description: "Cell tower monitoring and network optimization insights",
    color: "text-cyan-400",
    bgColor: "bg-cyan-900/20",
  },
  {
    icon: Globe,
    title: "International Insights",
    description: "Global communication patterns and country-wise analytics",
    color: "text-orange-400",
    bgColor: "bg-orange-900/20",
  },
  {
    icon: CreditCard,
    title: "Revenue Tracking",
    description: "Payment analytics with transaction history and trends",
    color: "text-yellow-400",
    bgColor: "bg-yellow-900/20",
  },
]

const services = [
  {
    icon: Users,
    title: "Subscriber Management",
    description: "Complete subscriber profiles with account details and activity history",
    metrics: "10K+ Subscribers",
  },
  {
    icon: Activity,
    title: "Performance Monitoring",
    description: "Real-time system health with network quality and efficiency tracking",
    metrics: "99.9% Uptime",
  },
  {
    icon: Database,
    title: "Data Intelligence",
    description: "Advanced analytics with bandwidth monitoring and usage optimization",
    metrics: "245MB Analyzed",
  },
  {
    icon: Shield,
    title: "Security & Compliance",
    description: "Enterprise-grade security with audit logging and access controls",
    metrics: "Bank-level Security",
  },
]

const stats = [
  { label: "Active Subscribers", value: "10,000+", icon: Users },
  { label: "Daily Transactions", value: "50K+", icon: CreditCard },
  { label: "Network Uptime", value: "99.9%", icon: Activity },
  { label: "Data Processed", value: "1TB+", icon: Database },
]

export default function LandingPage() {
  const [currentFeature, setCurrentFeature] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-950/80 to-transparent" />

        <div className="relative container mx-auto px-6 py-20">
          <div className="text-center space-y-8">
            {/* Logo and Brand */}
            <div className="flex items-center justify-center space-x-3 mb-8">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                <BarChart3 className="w-7 h-7 text-white" />
              </div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                TelecomPro
              </h1>
            </div>

            {/* Main Headline */}
            <div className="space-y-4">
              <h2 className="text-5xl md:text-7xl font-bold text-white leading-tight">
                Next-Generation
                <br />
                <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  Telecom Analytics
                </span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Comprehensive real-time analytics platform for telecom operators. Monitor subscribers, track revenue,
                analyze call patterns, and optimize network performance with enterprise-grade insights.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-8">
              <Link href="/dashboard">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg"
                >
                  <Play className="w-5 h-5 mr-2" />
                  Launch Platform
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Button
                variant="outline"
                size="lg"
                className="border-gray-600 text-gray-300 hover:bg-gray-800 px-8 py-4 text-lg bg-transparent"
              >
                <Activity className="w-5 h-5 mr-2" />
                View Demo
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center justify-center space-x-8 pt-12 opacity-60">
              <div className="flex items-center space-x-2">
                <Shield className="w-5 h-5 text-green-400" />
                <span className="text-sm text-gray-400">Enterprise Security</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-blue-400" />
                <span className="text-sm text-gray-400">Real-time Updates</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-yellow-400" />
                <span className="text-sm text-gray-400">99.9% Uptime</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-900/50">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                    <stat.icon className="w-6 h-6 text-blue-400" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-blue-900/20 text-blue-400 border-blue-400/20">Platform Features</Badge>
            <h3 className="text-4xl font-bold text-white mb-4">Powerful Analytics at Your Fingertips</h3>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Comprehensive suite of tools designed for modern telecom operations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className={`bg-gray-900 border-gray-800 hover:border-gray-700 transition-all duration-300 group cursor-pointer ${
                  index === currentFeature ? "ring-2 ring-blue-500/50" : ""
                }`}
              >
                <CardContent className="p-6">
                  <div
                    className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}
                  >
                    <feature.icon className={`w-6 h-6 ${feature.color}`} />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-2">{feature.title}</h4>
                  <p className="text-gray-400 leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-900/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-purple-900/20 text-purple-400 border-purple-400/20">Core Services</Badge>
            <h3 className="text-4xl font-bold text-white mb-4">Complete Telecom Management Suite</h3>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              End-to-end solutions for subscriber management, network monitoring, and business intelligence
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <Card
                key={index}
                className="bg-gray-900 border-gray-800 hover:bg-gray-800/50 transition-all duration-300"
              >
                <CardContent className="p-8">
                  <div className="flex items-start space-x-4">
                    <div className="w-14 h-14 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl flex items-center justify-center flex-shrink-0">
                      <service.icon className="w-7 h-7 text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-xl font-semibold text-white">{service.title}</h4>
                        <Badge variant="outline" className="text-green-400 border-green-400/20">
                          {service.metrics}
                        </Badge>
                      </div>
                      <p className="text-gray-400 leading-relaxed">{service.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Preview */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-green-900/20 text-green-400 border-green-400/20">Platform Preview</Badge>
            <h3 className="text-4xl font-bold text-white mb-4">See TelecomPro in Action</h3>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Explore our comprehensive dashboard with real-time data visualization
            </p>
          </div>

          <div className="relative max-w-6xl mx-auto">
            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-gray-800">
              <div className="bg-gray-900 rounded-xl overflow-hidden border border-gray-800">
                {/* Mock Browser Header */}
                <div className="bg-gray-800 px-4 py-3 flex items-center space-x-2">
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="flex-1 bg-gray-700 rounded px-3 py-1 text-sm text-gray-400 ml-4">
                    telecompro.analytics/dashboard
                  </div>
                </div>

                {/* Mock Dashboard Content */}
                <div className="p-6 space-y-6">
                  <div className="flex items-center justify-between">
                    <h4 className="text-2xl font-bold text-white">Analytics Dashboard</h4>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-sm text-green-400">Live Data</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {[
                      { label: "Total Revenue", value: "DH 150", color: "text-green-400" },
                      { label: "Active Calls", value: "30", color: "text-blue-400" },
                      { label: "SMS Messages", value: "10", color: "text-purple-400" },
                      { label: "Data Usage", value: "245 MB", color: "text-orange-400" },
                    ].map((metric, index) => (
                      <div key={index} className="bg-gray-800 rounded-lg p-4">
                        <p className="text-gray-400 text-sm">{metric.label}</p>
                        <p className={`text-2xl font-bold ${metric.color}`}>{metric.value}</p>
                      </div>
                    ))}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-gray-800 rounded-lg p-4">
                      <h5 className="text-white font-medium mb-3">Call Activity</h5>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Local Calls</span>
                          <span className="text-blue-400">25</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div className="bg-blue-400 h-2 rounded-full" style={{ width: "83%" }}></div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-gray-800 rounded-lg p-4">
                      <h5 className="text-white font-medium mb-3">Network Status</h5>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span className="text-green-400">All Systems Operational</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-900/20 to-purple-900/20">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h3 className="text-4xl font-bold text-white">Ready to Transform Your Telecom Operations?</h3>
            <p className="text-xl text-gray-300">
              Join thousands of telecom operators who trust TelecomPro for their analytics needs
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link href="/dashboard">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg"
                >
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Access Platform Now
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </div>

            <div className="flex items-center justify-center space-x-8 pt-8 opacity-60">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="text-sm text-gray-400">No Setup Required</span>
              </div>
              <div className="flex items-center space-x-2">
                <Star className="w-5 h-5 text-yellow-400" />
                <span className="text-sm text-gray-400">Enterprise Ready</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-blue-400" />
                <span className="text-sm text-gray-400">Instant Access</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-950 border-t border-gray-800">
        <div className="container mx-auto px-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">TelecomPro</span>
            </div>
            <div className="text-gray-400 text-sm">© 2024 TelecomPro Analytics. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>
  )
}
