// Advanced Fraud Detection Algorithms for Telecom Platform

export interface CallRecord {
  id: string
  msisdn: string
  calledNumber: string
  callType: 'incoming' | 'outgoing'
  duration: number // in seconds
  timestamp: Date
  cellTower: string
  imei: string
}

export interface FraudIndicators {
  simboxProbability: number
  imeiSwitchingRisk: number
  geographicAnomalyRisk: number
  callPatternRisk: number
  networkBehaviorRisk: number
  overallRiskScore: number
  fraudTypes: string[]
  alerts: FraudAlert[]
}

export interface FraudAlert {
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  message: string
  timestamp: Date
  confidence: number
}

export class FraudDetectionEngine {
  
  /**
   * SIMbox Detection Algorithm
   * Detects automated call routing through SIM boxes
   */
  static detectSIMboxFraud(calls: CallRecord[]): {
    probability: number
    indicators: string[]
    score: number
  } {
    const indicators: string[] = []
    let score = 0

    // 1. Short call duration analysis
    const shortCalls = calls.filter(call => call.duration < 60 && call.callType === 'outgoing')
    const shortCallPercentage = (shortCalls.length / calls.length) * 100

    if (shortCallPercentage > 50) {
      indicators.push('HIGH_SHORT_CALL_RATIO')
      score += 3
    } else if (shortCallPercentage > 30) {
      indicators.push('MEDIUM_SHORT_CALL_RATIO')
      score += 2
    }

    // 2. No incoming calls pattern
    const incomingCalls = calls.filter(call => call.callType === 'incoming')
    const incomingRatio = incomingCalls.length / calls.length

    if (incomingRatio === 0 && calls.length > 10) {
      indicators.push('NO_INCOMING_CALLS')
      score += 4
    } else if (incomingRatio < 0.1) {
      indicators.push('LOW_INCOMING_CALLS')
      score += 2
    }

    // 3. High volume in short time periods
    const callsPerHour = this.calculateCallsPerHour(calls)
    const maxCallsPerHour = Math.max(...Object.values(callsPerHour))

    if (maxCallsPerHour > 50) {
      indicators.push('EXTREME_CALL_VOLUME')
      score += 3
    } else if (maxCallsPerHour > 30) {
      indicators.push('HIGH_CALL_VOLUME')
      score += 2
    }

    // 4. Distinct number ratio (calling many different numbers)
    const uniqueNumbers = new Set(calls.map(call => call.calledNumber)).size
    const distinctRatio = uniqueNumbers / calls.length

    if (distinctRatio > 0.8) {
      indicators.push('HIGH_DISTINCT_RATIO')
      score += 2
    }

    // 5. Regular time patterns (automated behavior)
    const timePatterns = this.analyzeTimePatterns(calls)
    if (timePatterns.isRegular) {
      indicators.push('REGULAR_TIME_PATTERN')
      score += 1
    }

    const probability = Math.min(score * 10, 100)
    return { probability, indicators, score }
  }

  /**
   * IMEI Switching Detection
   * Detects frequent device changes which may indicate fraud
   */
  static detectIMEISwitching(calls: CallRecord[]): {
    riskScore: number
    switchCount: number
    frequency: 'LOW' | 'MEDIUM' | 'HIGH'
    indicators: string[]
  } {
    const indicators: string[] = []
    const imeiChanges = this.calculateIMEIChanges(calls)
    const switchCount = imeiChanges.length
    const timeSpan = this.getTimeSpan(calls)
    const daysSpan = timeSpan / (1000 * 60 * 60 * 24)

    let riskScore = 0
    let frequency: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW'

    // Calculate switching frequency
    const switchesPerDay = switchCount / daysSpan

    if (switchesPerDay > 2) {
      frequency = 'HIGH'
      riskScore = 8
      indicators.push('RAPID_IMEI_SWITCHING')
    } else if (switchesPerDay > 0.5) {
      frequency = 'MEDIUM'
      riskScore = 5
      indicators.push('FREQUENT_IMEI_SWITCHING')
    } else if (switchCount > 0) {
      frequency = 'LOW'
      riskScore = 2
      indicators.push('OCCASIONAL_IMEI_SWITCHING')
    }

    // Check for suspicious patterns
    if (switchCount > 5) {
      indicators.push('MULTIPLE_DEVICE_USAGE')
      riskScore += 2
    }

    // Check for very short usage periods
    const avgUsageDays = daysSpan / (switchCount + 1)
    if (avgUsageDays < 1) {
      indicators.push('SHORT_DEVICE_USAGE')
      riskScore += 2
    }

    return { riskScore: Math.min(riskScore, 10), switchCount, frequency, indicators }
  }

  /**
   * Geographic Anomaly Detection
   * Detects impossible movement patterns
   */
  static detectGeographicAnomalies(calls: CallRecord[]): {
    riskScore: number
    impossibleMovements: number
    maxSpeed: number
    indicators: string[]
  } {
    const indicators: string[] = []
    let riskScore = 0
    let impossibleMovements = 0
    let maxSpeed = 0

    // Sort calls by timestamp
    const sortedCalls = calls.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())

    for (let i = 1; i < sortedCalls.length; i++) {
      const prevCall = sortedCalls[i - 1]
      const currentCall = sortedCalls[i]

      // Calculate time difference in hours
      const timeDiff = (currentCall.timestamp.getTime() - prevCall.timestamp.getTime()) / (1000 * 60 * 60)

      // If different cell towers, calculate potential speed
      if (prevCall.cellTower !== currentCall.cellTower && timeDiff > 0) {
        // Assume average distance between towers is 5-20km
        const estimatedDistance = this.estimateDistance(prevCall.cellTower, currentCall.cellTower)
        const speed = estimatedDistance / timeDiff

        maxSpeed = Math.max(maxSpeed, speed)

        // Flag impossible speeds (>200 km/h for extended periods)
        if (speed > 200 && timeDiff < 0.5) {
          impossibleMovements++
          indicators.push('IMPOSSIBLE_MOVEMENT')
          riskScore += 2
        } else if (speed > 100) {
          indicators.push('HIGH_SPEED_MOVEMENT')
          riskScore += 1
        }
      }
    }

    // Check for too many location changes
    const uniqueTowers = new Set(calls.map(call => call.cellTower)).size
    if (uniqueTowers > calls.length * 0.5) {
      indicators.push('EXCESSIVE_LOCATION_CHANGES')
      riskScore += 1
    }

    return { 
      riskScore: Math.min(riskScore, 10), 
      impossibleMovements, 
      maxSpeed: Math.round(maxSpeed), 
      indicators 
    }
  }

  /**
   * Call Pattern Analysis
   * Detects unusual calling patterns that may indicate fraud
   */
  static analyzeCallPatterns(calls: CallRecord[]): {
    riskScore: number
    patterns: string[]
    anomalies: number
  } {
    const patterns: string[] = []
    let riskScore = 0
    let anomalies = 0

    // 1. Burst calling pattern
    const burstPatterns = this.detectBurstPatterns(calls)
    if (burstPatterns.count > 3) {
      patterns.push('BURST_CALLING')
      riskScore += 2
      anomalies += burstPatterns.count
    }

    // 2. Regular interval calling (bot-like behavior)
    const intervalPattern = this.detectRegularIntervals(calls)
    if (intervalPattern.isRegular) {
      patterns.push('REGULAR_INTERVALS')
      riskScore += 2
      anomalies++
    }

    // 3. Unusual time distribution
    const timeDistribution = this.analyzeTimeDistribution(calls)
    if (timeDistribution.isUnusual) {
      patterns.push('UNUSUAL_TIME_DISTRIBUTION')
      riskScore += 1
      anomalies++
    }

    // 4. Sequential number calling
    const sequentialPattern = this.detectSequentialCalling(calls)
    if (sequentialPattern.isSequential) {
      patterns.push('SEQUENTIAL_CALLING')
      riskScore += 2
      anomalies++
    }

    return { riskScore: Math.min(riskScore, 10), patterns, anomalies }
  }

  /**
   * Network Behavior Analysis
   * Analyzes network usage patterns for fraud indicators
   */
  static analyzeNetworkBehavior(calls: CallRecord[]): {
    riskScore: number
    indicators: string[]
    networkDensity: number
  } {
    const indicators: string[] = []
    let riskScore = 0

    // Calculate network density
    const uniqueContacts = new Set(calls.map(call => call.calledNumber)).size
    const networkDensity = uniqueContacts / calls.length

    // High network density (calling many different numbers) can indicate SIMbox
    if (networkDensity > 0.8) {
      indicators.push('HIGH_NETWORK_DENSITY')
      riskScore += 2
    }

    // Check for circular calling patterns
    const circularPattern = this.detectCircularCalling(calls)
    if (circularPattern.isCircular) {
      indicators.push('CIRCULAR_CALLING')
      riskScore += 3
    }

    // Check for one-way communication
    const bidirectionalRatio = this.calculateBidirectionalRatio(calls)
    if (bidirectionalRatio < 0.1) {
      indicators.push('ONE_WAY_COMMUNICATION')
      riskScore += 2
    }

    return { riskScore: Math.min(riskScore, 10), indicators, networkDensity }
  }

  /**
   * Comprehensive Fraud Assessment
   * Combines all detection algorithms for overall risk assessment
   */
  static assessFraudRisk(calls: CallRecord[]): FraudIndicators {
    const simbox = this.detectSIMboxFraud(calls)
    const imeiSwitching = this.detectIMEISwitching(calls)
    const geographic = this.detectGeographicAnomalies(calls)
    const callPatterns = this.analyzeCallPatterns(calls)
    const networkBehavior = this.analyzeNetworkBehavior(calls)

    // Calculate weighted overall risk score
    const overallRiskScore = (
      simbox.score * 0.3 +
      imeiSwitching.riskScore * 0.25 +
      geographic.riskScore * 0.2 +
      callPatterns.riskScore * 0.15 +
      networkBehavior.riskScore * 0.1
    )

    // Determine fraud types
    const fraudTypes: string[] = []
    if (simbox.probability > 50) fraudTypes.push('SIMBOX')
    if (imeiSwitching.frequency === 'HIGH') fraudTypes.push('IMEI_SWITCHING')
    if (geographic.impossibleMovements > 0) fraudTypes.push('GEOGRAPHIC_ANOMALY')
    if (callPatterns.patterns.length > 2) fraudTypes.push('PATTERN_ANOMALY')

    // Generate alerts
    const alerts: FraudAlert[] = []
    if (overallRiskScore > 7) {
      alerts.push({
        type: 'HIGH_FRAUD_RISK',
        severity: 'CRITICAL',
        message: 'Multiple fraud indicators detected. Immediate investigation required.',
        timestamp: new Date(),
        confidence: 0.9
      })
    }

    return {
      simboxProbability: simbox.probability,
      imeiSwitchingRisk: imeiSwitching.riskScore,
      geographicAnomalyRisk: geographic.riskScore,
      callPatternRisk: callPatterns.riskScore,
      networkBehaviorRisk: networkBehavior.riskScore,
      overallRiskScore: Math.round(overallRiskScore * 10) / 10,
      fraudTypes,
      alerts
    }
  }

  // Helper methods
  private static calculateCallsPerHour(calls: CallRecord[]): Record<string, number> {
    const hourCounts: Record<string, number> = {}
    calls.forEach(call => {
      const hour = call.timestamp.toISOString().slice(0, 13) // YYYY-MM-DDTHH
      hourCounts[hour] = (hourCounts[hour] || 0) + 1
    })
    return hourCounts
  }

  private static analyzeTimePatterns(calls: CallRecord[]): { isRegular: boolean } {
    // Simplified implementation - check if calls happen at very regular intervals
    if (calls.length < 3) return { isRegular: false }
    
    const intervals = []
    for (let i = 1; i < calls.length; i++) {
      const interval = calls[i].timestamp.getTime() - calls[i-1].timestamp.getTime()
      intervals.push(interval)
    }
    
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length
    
    // If variance is very low, it suggests regular automated behavior
    return { isRegular: variance < avgInterval * 0.1 }
  }

  private static calculateIMEIChanges(calls: CallRecord[]): Array<{from: string, to: string, timestamp: Date}> {
    const changes = []
    let currentIMEI = calls[0]?.imei
    
    for (const call of calls) {
      if (call.imei !== currentIMEI) {
        changes.push({
          from: currentIMEI,
          to: call.imei,
          timestamp: call.timestamp
        })
        currentIMEI = call.imei
      }
    }
    
    return changes
  }

  private static getTimeSpan(calls: CallRecord[]): number {
    if (calls.length === 0) return 0
    const timestamps = calls.map(call => call.timestamp.getTime())
    return Math.max(...timestamps) - Math.min(...timestamps)
  }

  private static estimateDistance(tower1: string, tower2: string): number {
    // Simplified distance estimation - in real implementation, use actual tower coordinates
    return Math.random() * 20 + 5 // 5-25 km random distance
  }

  private static detectBurstPatterns(calls: CallRecord[]): { count: number } {
    // Detect periods of high call volume in short time windows
    let burstCount = 0
    const timeWindow = 10 * 60 * 1000 // 10 minutes
    
    for (let i = 0; i < calls.length; i++) {
      const windowStart = calls[i].timestamp.getTime()
      const windowEnd = windowStart + timeWindow
      const callsInWindow = calls.filter(call => 
        call.timestamp.getTime() >= windowStart && call.timestamp.getTime() <= windowEnd
      ).length
      
      if (callsInWindow > 10) burstCount++
    }
    
    return { count: burstCount }
  }

  private static detectRegularIntervals(calls: CallRecord[]): { isRegular: boolean } {
    return this.analyzeTimePatterns(calls)
  }

  private static analyzeTimeDistribution(calls: CallRecord[]): { isUnusual: boolean } {
    // Check if calls are concentrated in unusual hours (e.g., all at night)
    const hourCounts = Array(24).fill(0)
    calls.forEach(call => {
      const hour = call.timestamp.getHours()
      hourCounts[hour]++
    })
    
    // Check if >80% of calls are in night hours (10PM - 6AM)
    const nightHours = [22, 23, 0, 1, 2, 3, 4, 5]
    const nightCalls = nightHours.reduce((sum, hour) => sum + hourCounts[hour], 0)
    const nightRatio = nightCalls / calls.length
    
    return { isUnusual: nightRatio > 0.8 }
  }

  private static detectSequentialCalling(calls: CallRecord[]): { isSequential: boolean } {
    // Check if called numbers follow a sequential pattern
    const outgoingCalls = calls.filter(call => call.callType === 'outgoing')
    if (outgoingCalls.length < 5) return { isSequential: false }
    
    let sequentialCount = 0
    for (let i = 1; i < outgoingCalls.length; i++) {
      const prev = parseInt(outgoingCalls[i-1].calledNumber.replace(/\D/g, ''))
      const current = parseInt(outgoingCalls[i].calledNumber.replace(/\D/g, ''))
      
      if (!isNaN(prev) && !isNaN(current) && Math.abs(current - prev) === 1) {
        sequentialCount++
      }
    }
    
    return { isSequential: sequentialCount > outgoingCalls.length * 0.3 }
  }

  private static detectCircularCalling(calls: CallRecord[]): { isCircular: boolean } {
    // Detect if the same numbers call each other back and forth
    const callPairs = new Map<string, number>()
    
    calls.forEach(call => {
      const pair = [call.msisdn, call.calledNumber].sort().join('-')
      callPairs.set(pair, (callPairs.get(pair) || 0) + 1)
    })
    
    // If any pair has >10 calls, consider it circular
    return Array.from(callPairs.values()).some(count => count > 10)
  }

  private static calculateBidirectionalRatio(calls: CallRecord[]): number {
    const outgoing = calls.filter(call => call.callType === 'outgoing')
    const incoming = calls.filter(call => call.callType === 'incoming')
    
    if (outgoing.length === 0) return 0
    return incoming.length / outgoing.length
  }
}
