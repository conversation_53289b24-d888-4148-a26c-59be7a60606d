"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Users, UserPlus, TrendingUp, Search, Filter, Download, Phone, Calendar } from "lucide-react"

export default function DealersPage() {
  const [dealerData, setDealerData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/dealer-activations.json")
        const data = await response.json()
        setDealerData(data)
      } catch (error) {
        console.error("Error fetching dealer data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-500"></div>
      </div>
    )
  }

  const frdActivations = dealerData?.activations?.filter((a: any) => a.type.includes("FRD")).length || 0
  const nfrActivations = dealerData?.activations?.filter((a: any) => a.type.includes("NFR")).length || 0

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Dealer Management</h1>
          <p className="text-gray-400 mt-2">Monitor dealer activations and performance (Last 7 Days)</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <Card className="bg-gray-900 border-gray-800">
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search dealers, phone numbers, activation types..."
              className="pl-10 bg-gray-800 border-gray-700 text-white placeholder-gray-400"
            />
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Activations</p>
                <p className="text-2xl font-bold text-yellow-400">{dealerData?.activationsCount}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <p className="text-sm text-green-400">+15.2%</p>
                </div>
              </div>
              <UserPlus className="w-8 h-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">FRD Activations</p>
                <p className="text-2xl font-bold text-green-400">{frdActivations}</p>
                <p className="text-sm text-gray-400">
                  {((frdActivations / dealerData?.activationsCount) * 100).toFixed(1)}%
                </p>
              </div>
              <Users className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">NFR Activations</p>
                <p className="text-2xl font-bold text-blue-400">{nfrActivations}</p>
                <p className="text-sm text-gray-400">
                  {((nfrActivations / dealerData?.activationsCount) * 100).toFixed(1)}%
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Daily Average</p>
                <p className="text-2xl font-bold text-purple-400">{(dealerData?.activationsCount / 7).toFixed(1)}</p>
                <p className="text-sm text-gray-400">Per day</p>
              </div>
              <Calendar className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dealer Information */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center text-yellow-400">
            <Users className="w-5 h-5 mr-2" />
            Dealer {dealerData?.dealer} - Performance Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <p className="text-sm text-gray-400">Dealer Code</p>
              <p className="font-mono text-xl text-yellow-400">{dealerData?.dealer}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-400">Performance Rating</p>
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">Excellent</Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-400">Status</p>
              <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">Active</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activations List */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center text-green-400">
            <UserPlus className="w-5 h-5 mr-2" />
            Recent Activations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {dealerData?.activations?.map((activation: any, index: number) => (
              <div key={index}>
                <div className="flex items-center justify-between py-3">
                  <div className="flex items-center space-x-4">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        activation.type.includes("FRD") ? "bg-green-900/20" : "bg-blue-900/20"
                      }`}
                    >
                      <Phone
                        className={`w-5 h-5 ${activation.type.includes("FRD") ? "text-green-400" : "text-blue-400"}`}
                      />
                    </div>
                    <div>
                      <p className="font-medium text-white">{activation.phone}</p>
                      <p className="text-sm text-gray-400">{activation.date || "Date not available"}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge
                      variant="outline"
                      className={
                        activation.type.includes("FRD")
                          ? "text-green-400 border-green-400/20"
                          : "text-blue-400 border-blue-400/20"
                      }
                    >
                      {activation.type}
                    </Badge>
                    <p className="text-xs text-gray-500 mt-1">Activation #{index + 1}</p>
                  </div>
                </div>
                {index < dealerData.activations.length - 1 && <Separator className="bg-gray-800" />}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-purple-400">Activation Type Distribution</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">FRD Activations</span>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-800 rounded-full h-3">
                  <div
                    className="bg-green-400 h-3 rounded-full"
                    style={{
                      width: `${(frdActivations / dealerData?.activationsCount) * 100}%`,
                    }}
                  ></div>
                </div>
                <span className="text-sm text-green-400 font-mono">
                  {frdActivations} ({((frdActivations / dealerData?.activationsCount) * 100).toFixed(1)}%)
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-400">NFR Activations</span>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-800 rounded-full h-3">
                  <div
                    className="bg-blue-400 h-3 rounded-full"
                    style={{
                      width: `${(nfrActivations / dealerData?.activationsCount) * 100}%`,
                    }}
                  ></div>
                </div>
                <span className="text-sm text-blue-400 font-mono">
                  {nfrActivations} ({((nfrActivations / dealerData?.activationsCount) * 100).toFixed(1)}%)
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-cyan-400">Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Success Rate</span>
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">98.7%</Badge>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Avg Processing Time</span>
              <span className="font-mono text-cyan-400">2.3 min</span>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Weekly Target</span>
              <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">160% Achieved</Badge>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Quality Score</span>
              <span className="font-mono text-green-400">9.2/10</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
