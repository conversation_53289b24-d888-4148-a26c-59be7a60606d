"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { AdvancedFraudFilters, InvestigationWorkflow, FraudFilters } from "@/components/fraud-investigation-tools"
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Shield,
  Target,
  Activity,
  Users,
  MapPin,
  Clock,
  Smartphone,
  Eye,
  Zap,
} from "lucide-react"

// Mock analytics data
const fraudAnalytics = {
  trends: {
    totalCases: 1247,
    weeklyChange: 12.5,
    avgResolutionTime: "2.3 hours",
    falsePositiveRate: 8.2
  },
  patterns: {
    simboxCases: 342,
    imeiSwitching: 198,
    geographicAnomalies: 156,
    highVolume: 289,
    other: 262
  },
  geographic: {
    hotspots: [
      { city: "Casablanca", cases: 423, riskLevel: "HIGH" },
      { city: "Rabat", cases: 287, riskLevel: "MEDIUM" },
      { city: "Marrakech", cases: 198, riskLevel: "MEDIUM" },
      { city: "Fez", cases: 156, riskLevel: "LOW" },
      { city: "Tangier", cases: 183, riskLevel: "MEDIUM" }
    ]
  },
  timeAnalysis: {
    peakHours: [
      { hour: "09:00", cases: 89 },
      { hour: "14:00", cases: 76 },
      { hour: "19:00", cases: 92 },
      { hour: "22:00", cases: 134 }
    ],
    weeklyPattern: {
      monday: 178,
      tuesday: 156,
      wednesday: 189,
      thursday: 167,
      friday: 198,
      saturday: 203,
      sunday: 156
    }
  }
}

export default function FraudAnalyticsPage() {
  const [activeFilters, setActiveFilters] = useState<FraudFilters>({
    riskScoreRange: [0, 10],
    fraudTypes: [],
    timeRange: "24h",
    location: "",
    imeiChanges: 0,
    callVolumeMin: 0,
    shortCallThreshold: 30,
    noIncomingOnly: false,
    geographicAnomalies: false,
    deviceTypes: [],
    investigationStatus: []
  })

  const handleFiltersChange = (filters: FraudFilters) => {
    setActiveFilters(filters)
    // In real implementation, this would trigger data refresh
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Fraud Analytics Dashboard</h1>
          <p className="text-gray-400 mt-2">Advanced analytics and investigation tools for fraud detection</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm" className="bg-blue-900/20 border-blue-400/20 text-blue-400">
            <BarChart3 className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Cases</p>
                <p className="text-3xl font-bold text-blue-400">{fraudAnalytics.trends.totalCases}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                  <span className="text-xs text-green-400">+{fraudAnalytics.trends.weeklyChange}% this week</span>
                </div>
              </div>
              <BarChart3 className="w-10 h-10 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Avg Resolution Time</p>
                <p className="text-3xl font-bold text-green-400">{fraudAnalytics.trends.avgResolutionTime}</p>
                <div className="flex items-center mt-1">
                  <TrendingDown className="w-3 h-3 text-green-400 mr-1" />
                  <span className="text-xs text-green-400">-15% improvement</span>
                </div>
              </div>
              <Clock className="w-10 h-10 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">False Positive Rate</p>
                <p className="text-3xl font-bold text-yellow-400">{fraudAnalytics.trends.falsePositiveRate}%</p>
                <div className="flex items-center mt-1">
                  <TrendingDown className="w-3 h-3 text-green-400 mr-1" />
                  <span className="text-xs text-green-400">-2.1% this month</span>
                </div>
              </div>
              <Shield className="w-10 h-10 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">SIMbox Detection</p>
                <p className="text-3xl font-bold text-red-400">{fraudAnalytics.patterns.simboxCases}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 text-red-400 mr-1" />
                  <span className="text-xs text-red-400">+8% this week</span>
                </div>
              </div>
              <Target className="w-10 h-10 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Interface */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-gray-900 border border-gray-800">
          <TabsTrigger value="overview" className="data-[state=active]:bg-blue-600">
            <BarChart3 className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="patterns" className="data-[state=active]:bg-red-600">
            <Target className="w-4 h-4 mr-2" />
            Fraud Patterns
          </TabsTrigger>
          <TabsTrigger value="geographic" className="data-[state=active]:bg-green-600">
            <MapPin className="w-4 h-4 mr-2" />
            Geographic Analysis
          </TabsTrigger>
          <TabsTrigger value="investigation" className="data-[state=active]:bg-purple-600">
            <Eye className="w-4 h-4 mr-2" />
            Investigation Tools
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Fraud Type Distribution */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-white">
                  <Target className="w-5 h-5 mr-2" />
                  Fraud Type Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">SIMbox Fraud</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-700 rounded-full h-2">
                        <div className="bg-red-400 h-2 rounded-full" style={{width: '27%'}}></div>
                      </div>
                      <span className="text-red-400 font-mono">{fraudAnalytics.patterns.simboxCases}</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">High Volume</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-700 rounded-full h-2">
                        <div className="bg-orange-400 h-2 rounded-full" style={{width: '23%'}}></div>
                      </div>
                      <span className="text-orange-400 font-mono">{fraudAnalytics.patterns.highVolume}</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">IMEI Switching</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-700 rounded-full h-2">
                        <div className="bg-yellow-400 h-2 rounded-full" style={{width: '16%'}}></div>
                      </div>
                      <span className="text-yellow-400 font-mono">{fraudAnalytics.patterns.imeiSwitching}</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Geographic Anomalies</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-700 rounded-full h-2">
                        <div className="bg-green-400 h-2 rounded-full" style={{width: '12%'}}></div>
                      </div>
                      <span className="text-green-400 font-mono">{fraudAnalytics.patterns.geographicAnomalies}</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Other</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-700 rounded-full h-2">
                        <div className="bg-purple-400 h-2 rounded-full" style={{width: '21%'}}></div>
                      </div>
                      <span className="text-purple-400 font-mono">{fraudAnalytics.patterns.other}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Time Analysis */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-white">
                  <Clock className="w-5 h-5 mr-2" />
                  Peak Activity Hours
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {fraudAnalytics.timeAnalysis.peakHours.map((hour, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-400">{hour.hour}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-blue-400 h-2 rounded-full" 
                            style={{width: `${(hour.cases / 134) * 100}%`}}
                          ></div>
                        </div>
                        <span className="text-blue-400 font-mono w-8">{hour.cases}</span>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-4 p-3 bg-blue-900/10 rounded-lg border border-blue-400/20">
                  <p className="text-blue-400 text-sm font-medium">Peak Activity</p>
                  <p className="text-blue-300 text-xs">
                    Highest fraud activity detected between 10 PM - 11 PM
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="geographic" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <MapPin className="w-5 h-5 mr-2" />
                Geographic Fraud Hotspots
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fraudAnalytics.geographic.hotspots.map((location, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                      <div>
                        <p className="text-white font-medium">{location.city}</p>
                        <p className="text-gray-400 text-sm">{location.cases} cases detected</p>
                      </div>
                    </div>
                    <Badge className={`${
                      location.riskLevel === 'HIGH' ? 'bg-red-900/20 text-red-400 border-red-400/20' :
                      location.riskLevel === 'MEDIUM' ? 'bg-yellow-900/20 text-yellow-400 border-yellow-400/20' :
                      'bg-green-900/20 text-green-400 border-green-400/20'
                    }`}>
                      {location.riskLevel} RISK
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="investigation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AdvancedFraudFilters onFiltersChange={handleFiltersChange} />
            <InvestigationWorkflow />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
