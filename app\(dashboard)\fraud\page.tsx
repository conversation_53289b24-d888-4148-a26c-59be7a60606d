"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertTriangle,
  Shield,
  Eye,
  Target,
  Activity,
  MapPin,
  Smartphone,
  PhoneIncoming,
  PhoneOutgoing,
  Clock,
  Users,
  Globe,
  BarChart3,
  Search,
  Filter,
  Download,
  Bell,
  Zap,
  TrendingUp,
  Calendar,
  FileText,
} from "lucide-react"

export default function FraudDetectionPage() {
  const [fraudData, setFraudData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [selectedTimeRange, setSelectedTimeRange] = useState("24h")
  const [riskFilter, setRiskFilter] = useState("all")

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [localRes, internationalRes, imeiRes, subscriberRes] = await Promise.all([
          fetch("/api/enhanced-call-activity.json"),
          fetch("/api/enhanced-international-call-activity.json"),
          fetch("/api/imei-tracking.json"),
          fetch("/api/enhanced-subscriber-profile.json"),
        ])

        const [localData, internationalData, imeiData, subscriberData] = await Promise.all([
          localRes.json(),
          internationalRes.json(),
          imeiRes.json(),
          subscriberRes.json()
        ])

        setFraudData({
          local: localData,
          international: internationalData,
          imei: imeiData,
          subscriber: subscriberData
        })
      } catch (error) {
        console.error("Error fetching fraud data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-500"></div>
      </div>
    )
  }

  const calculateOverallRisk = () => {
    const localRisk = fraudData?.local?.riskScore || 0
    const internationalRisk = fraudData?.international?.riskScore || 0
    const imeiRisk = fraudData?.imei?.riskScore || 0
    const subscriberRisk = fraudData?.subscriber?.fraudProfile?.overallRiskScore || 0
    return ((localRisk + internationalRisk + imeiRisk + subscriberRisk) / 4).toFixed(1)
  }

  const overallRisk = calculateOverallRisk()
  const getRiskLevel = (score: number) => {
    if (score >= 7) return { level: "HIGH", color: "text-red-400", bgColor: "bg-red-900/20", borderColor: "border-red-400/20" }
    if (score >= 4) return { level: "MEDIUM", color: "text-yellow-400", bgColor: "bg-yellow-900/20", borderColor: "border-yellow-400/20" }
    return { level: "LOW", color: "text-green-400", bgColor: "bg-green-900/20", borderColor: "border-green-400/20" }
  }

  const riskInfo = getRiskLevel(parseFloat(overallRisk))

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Fraud Detection Center</h1>
          <p className="text-gray-400 mt-2">Advanced fraud analytics and investigation tools</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm" className="bg-red-900/20 border-red-400/20 text-red-400 hover:bg-red-900/30">
            <Bell className="w-4 h-4 mr-2" />
            Create Alert
          </Button>
          <Button variant="outline" size="sm" className="bg-blue-900/20 border-blue-400/20 text-blue-400">
            <FileText className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Investigation Controls */}
      <Card className="bg-gray-900 border-gray-800">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Search className="w-4 h-4 text-gray-400" />
                <Input 
                  placeholder="Search MSISDN, IMEI, or Case ID..." 
                  className="w-64 bg-gray-800 border-gray-700"
                />
              </div>
              <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                <SelectTrigger className="w-32 bg-gray-800 border-gray-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="24h">Last 24h</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                </SelectContent>
              </Select>
              <Select value={riskFilter} onValueChange={setRiskFilter}>
                <SelectTrigger className="w-32 bg-gray-800 border-gray-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Risk</SelectItem>
                  <SelectItem value="high">High Risk</SelectItem>
                  <SelectItem value="medium">Medium Risk</SelectItem>
                  <SelectItem value="low">Low Risk</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">
                <Activity className="w-3 h-3 mr-1" />
                Live Monitoring
              </Badge>
              <Button size="sm" variant="outline" className="h-8">
                <Filter className="w-4 h-4 mr-1" />
                Advanced Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Critical Alerts */}
      {parseFloat(overallRisk) >= 7 && (
        <Card className="bg-red-900/20 border-red-400/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-6 h-6 text-red-400" />
                <div>
                  <p className="text-red-400 font-semibold">Critical Fraud Alert</p>
                  <p className="text-red-300 text-sm">
                    Multiple high-risk patterns detected. Risk Score: {overallRisk}/10
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button size="sm" className="bg-red-600 hover:bg-red-700">
                  Block Subscriber
                </Button>
                <Button size="sm" variant="outline" className="border-red-400/20 text-red-400">
                  Investigate
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fraud Risk Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className={`bg-gray-900 border-gray-800 ${riskInfo.borderColor}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Overall Risk Score</p>
                <p className={`text-3xl font-bold ${riskInfo.color}`}>{overallRisk}/10</p>
                <Badge className={`${riskInfo.bgColor} ${riskInfo.color} ${riskInfo.borderColor} text-xs mt-2`}>
                  {riskInfo.level} RISK
                </Badge>
              </div>
              <Shield className={`w-10 h-10 ${riskInfo.color}`} />
            </div>
            <div className="mt-4">
              <Progress value={parseFloat(overallRisk) * 10} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active Alerts</p>
                <p className="text-3xl font-bold text-red-400">
                  {(fraudData?.imei?.alerts?.length || 0) + (fraudData?.local?.fraudIndicators?.suspiciousPatterns?.length || 0)}
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  {fraudData?.imei?.alerts?.filter((a: any) => a.severity === 'HIGH').length || 0} critical
                </p>
              </div>
              <Bell className="w-10 h-10 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">SIMbox Probability</p>
                <p className="text-3xl font-bold text-orange-400">
                  {fraudData?.local?.fraudIndicators?.shortCallsPercentage || 0}%
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  {fraudData?.local?.fraudIndicators?.shortCallsCount || 0} short calls detected
                </p>
              </div>
              <Target className="w-10 h-10 text-orange-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Investigation Score</p>
                <p className="text-3xl font-bold text-purple-400">8.2</p>
                <p className="text-xs text-gray-500 mt-2">
                  Requires immediate review
                </p>
              </div>
              <Eye className="w-10 h-10 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Fraud Analytics */}
      <Tabs defaultValue="fraud-patterns" className="space-y-6">
        <TabsList className="bg-gray-900 border border-gray-800">
          <TabsTrigger value="fraud-patterns" className="data-[state=active]:bg-red-600">
            <Target className="w-4 h-4 mr-2" />
            Fraud Patterns
          </TabsTrigger>
          <TabsTrigger value="simbox-detection" className="data-[state=active]:bg-orange-600">
            <Zap className="w-4 h-4 mr-2" />
            SIMbox Detection
          </TabsTrigger>
          <TabsTrigger value="device-analysis" className="data-[state=active]:bg-yellow-600">
            <Smartphone className="w-4 h-4 mr-2" />
            Device Analysis
          </TabsTrigger>
          <TabsTrigger value="network-behavior" className="data-[state=active]:bg-blue-600">
            <Activity className="w-4 h-4 mr-2" />
            Network Behavior
          </TabsTrigger>
          <TabsTrigger value="investigation" className="data-[state=active]:bg-purple-600">
            <Eye className="w-4 h-4 mr-2" />
            Investigation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="fraud-patterns" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Fraud Pattern Detection */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-red-400">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  Active Fraud Patterns
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {fraudData?.local?.fraudIndicators?.suspiciousPatterns?.map((pattern: string, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-red-900/10 rounded-lg border border-red-400/20">
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className="w-4 h-4 text-red-400" />
                      <div>
                        <span className="text-red-400 text-sm font-medium capitalize">
                          {pattern.replace('_', ' ')}
                        </span>
                        <p className="text-red-300 text-xs">
                          {pattern === 'short_duration_bursts' && 'Multiple calls under 1 minute detected'}
                          {pattern === 'high_distinct_ratio' && 'Unusually high number of unique contacts'}
                        </p>
                      </div>
                    </div>
                    <Badge className="bg-red-900/20 text-red-400 border-red-400/20">
                      Active
                    </Badge>
                  </div>
                ))}

                {fraudData?.imei?.alerts?.map((alert: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-orange-900/10 rounded-lg border border-orange-400/20">
                    <div className="flex items-center space-x-3">
                      <Smartphone className="w-4 h-4 text-orange-400" />
                      <div>
                        <span className="text-orange-400 text-sm font-medium">
                          {alert.type.replace('_', ' ')}
                        </span>
                        <p className="text-orange-300 text-xs">{alert.message}</p>
                      </div>
                    </div>
                    <Badge className={`${
                      alert.severity === 'HIGH' ? 'bg-red-900/20 text-red-400 border-red-400/20' :
                      'bg-orange-900/20 text-orange-400 border-orange-400/20'
                    }`}>
                      {alert.severity}
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Risk Score Breakdown */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-purple-400">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Risk Score Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Call Pattern Risk</span>
                    <span className="font-mono text-yellow-400">{fraudData?.local?.riskScore || 0}/10</span>
                  </div>
                  <Progress value={(fraudData?.local?.riskScore || 0) * 10} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Device Risk (IMEI)</span>
                    <span className="font-mono text-orange-400">{fraudData?.imei?.riskScore || 0}/10</span>
                  </div>
                  <Progress value={(fraudData?.imei?.riskScore || 0) * 10} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">International Risk</span>
                    <span className="font-mono text-green-400">{fraudData?.international?.riskScore || 0}/10</span>
                  </div>
                  <Progress value={(fraudData?.international?.riskScore || 0) * 10} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Subscriber Profile Risk</span>
                    <span className="font-mono text-blue-400">{fraudData?.subscriber?.fraudProfile?.overallRiskScore || 0}/10</span>
                  </div>
                  <Progress value={(fraudData?.subscriber?.fraudProfile?.overallRiskScore || 0) * 10} className="h-2" />
                </div>

                <div className="mt-6 p-4 bg-purple-900/10 rounded-lg border border-purple-400/20">
                  <h5 className="text-purple-400 font-medium mb-2">Risk Assessment Summary</h5>
                  <p className="text-purple-300 text-sm">
                    {parseFloat(overallRisk) >= 7 ?
                      "High fraud probability detected. Immediate investigation recommended." :
                      parseFloat(overallRisk) >= 4 ?
                      "Medium risk detected. Monitor closely for additional patterns." :
                      "Low risk profile. Continue routine monitoring."
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="simbox-detection" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* SIMbox Indicators */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-orange-400">
                  <Target className="w-5 h-5 mr-2" />
                  SIMbox Indicators
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Short Calls (&lt;1min)</span>
                    <div className="text-right">
                      <span className="font-mono text-red-400">{fraudData?.local?.fraudIndicators?.shortCallsCount || 0}</span>
                      <p className="text-xs text-gray-500">{fraudData?.local?.fraudIndicators?.shortCallsPercentage || 0}% of total</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">No Incoming Calls</span>
                    <div className="text-right">
                      <span className="font-mono text-yellow-400">{fraudData?.local?.fraudIndicators?.noIncomingCallsPeriods || 0}</span>
                      <p className="text-xs text-gray-500">periods detected</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">High Volume Spikes</span>
                    <div className="text-right">
                      <span className="font-mono text-orange-400">{fraudData?.local?.fraudIndicators?.highVolumeSpikes || 0}</span>
                      <p className="text-xs text-gray-500">spikes detected</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Max Calls/Hour</span>
                    <div className="text-right">
                      <span className="font-mono text-red-400">{fraudData?.local?.fraudIndicators?.maxCallsInHour || 0}</span>
                      <p className="text-xs text-gray-500">peak activity</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-orange-900/10 rounded-lg border border-orange-400/20">
                  <p className="text-orange-400 text-sm font-medium">SIMbox Probability</p>
                  <p className="text-2xl font-bold text-orange-400 mt-1">
                    {fraudData?.local?.fraudIndicators?.shortCallsPercentage || 0}%
                  </p>
                  <p className="text-orange-300 text-xs mt-1">
                    {(fraudData?.local?.fraudIndicators?.shortCallsPercentage || 0) > 30 ?
                      "High probability - Investigate immediately" :
                      (fraudData?.local?.fraudIndicators?.shortCallsPercentage || 0) > 15 ?
                      "Medium probability - Monitor closely" :
                      "Low probability - Normal usage pattern"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Call Pattern Timeline */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-400">
                  <Clock className="w-5 h-5 mr-2" />
                  Call Pattern Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Peak Hour Activity</span>
                    <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">
                      {fraudData?.local?.peakHour || 'N/A'}
                    </Badge>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Night Calls (10PM-6AM)</span>
                    <span className="font-mono text-orange-400">{fraudData?.local?.timeAnalysis?.nightCalls || 0}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Weekend Activity</span>
                    <span className="font-mono text-purple-400">{fraudData?.local?.timeAnalysis?.weekendCalls || 0}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Unusual Peak Activity</span>
                    <Badge className={`${fraudData?.local?.fraudIndicators?.unusualPeakActivity ? 'bg-red-900/20 text-red-400 border-red-400/20' : 'bg-green-900/20 text-green-400 border-green-400/20'}`}>
                      {fraudData?.local?.fraudIndicators?.unusualPeakActivity ? 'Detected' : 'Normal'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Network Analysis */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-cyan-400">
                  <Users className="w-5 h-5 mr-2" />
                  Network Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Incoming/Outgoing Ratio</span>
                    <span className="font-mono text-purple-400">{fraudData?.local?.ratioIncomingOutgoing || '0%'}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Distinct Number Ratio</span>
                    <span className="font-mono text-yellow-400">{fraudData?.local?.distinctRatio || '0%'}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Network Density</span>
                    <span className="font-mono text-cyan-400">
                      {((fraudData?.local?.contactNetwork?.networkDensity || 0) * 100).toFixed(0)}%
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Circular Calling</span>
                    <Badge className={`${fraudData?.local?.contactNetwork?.circularCalling ? 'bg-red-900/20 text-red-400 border-red-400/20' : 'bg-green-900/20 text-green-400 border-green-400/20'}`}>
                      {fraudData?.local?.contactNetwork?.circularCalling ? 'Detected' : 'None'}
                    </Badge>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-cyan-900/10 rounded-lg border border-cyan-400/20">
                  <p className="text-cyan-400 text-sm font-medium">Network Behavior Assessment</p>
                  <p className="text-cyan-300 text-xs mt-1">
                    {(fraudData?.local?.ratioIncomingOutgoing || '0%') === '0%' ?
                      "No incoming calls detected - High SIMbox indicator" :
                      "Normal bidirectional communication pattern"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="device-analysis" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* IMEI Analysis */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-orange-400">
                  <Smartphone className="w-5 h-5 mr-2" />
                  IMEI Switching Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Current IMEI</span>
                  <span className="font-mono text-orange-400 text-sm">{fraudData?.imei?.currentIMEI || 'N/A'}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total IMEI Count</span>
                  <span className="font-mono text-red-400 text-xl">{fraudData?.imei?.fraudIndicators?.totalIMEICount || 0}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Switch Frequency</span>
                  <Badge className={`${
                    fraudData?.imei?.fraudIndicators?.imeiSwitchFrequency === 'HIGH' ? 'bg-red-900/20 text-red-400 border-red-400/20' :
                    fraudData?.imei?.fraudIndicators?.imeiSwitchFrequency === 'MEDIUM' ? 'bg-yellow-900/20 text-yellow-400 border-yellow-400/20' :
                    'bg-green-900/20 text-green-400 border-green-400/20'
                  }`}>
                    {fraudData?.imei?.fraudIndicators?.imeiSwitchFrequency || 'NORMAL'}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Usage per IMEI</span>
                  <span className="font-mono text-yellow-400">{fraudData?.imei?.fraudIndicators?.avgIMEIUsageDays || 0} days</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Rapid Switching</span>
                  <Badge className={`${fraudData?.imei?.fraudIndicators?.rapidSwitching ? 'bg-red-900/20 text-red-400 border-red-400/20' : 'bg-green-900/20 text-green-400 border-green-400/20'}`}>
                    {fraudData?.imei?.fraudIndicators?.rapidSwitching ? 'Detected' : 'Normal'}
                  </Badge>
                </div>

                <div className="mt-4 p-3 bg-orange-900/10 rounded-lg border border-orange-400/20">
                  <p className="text-orange-400 text-sm font-medium">Device Risk Assessment</p>
                  <p className="text-orange-300 text-xs mt-1">
                    {fraudData?.imei?.fraudIndicators?.rapidSwitching ?
                      "Rapid IMEI switching detected - High fraud risk" :
                      "Normal IMEI usage pattern"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Device History */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-cyan-400">
                  <Activity className="w-5 h-5 mr-2" />
                  Device Usage History
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {fraudData?.imei?.imeiHistory?.slice(0, 4).map((device: any, index: number) => (
                  <div key={index} className="p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-mono text-cyan-400 text-sm">{device.imei}</span>
                      <Badge className={`${device.status === 'active' ? 'bg-green-900/20 text-green-400 border-green-400/20' : 'bg-gray-700 text-gray-400'}`}>
                        {device.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-gray-400">Calls:</span>
                        <span className="text-white ml-1">{device.callCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Duration:</span>
                        <span className="text-white ml-1">
                          {Math.ceil((new Date(device.lastSeen).getTime() - new Date(device.firstSeen).getTime()) / (1000 * 60 * 60 * 24))} days
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="investigation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Investigation Summary */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-purple-400">
                  <Eye className="w-5 h-5 mr-2" />
                  Investigation Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Case Priority</span>
                    <Badge className={`${parseFloat(overallRisk) >= 7 ? 'bg-red-900/20 text-red-400 border-red-400/20' : 'bg-yellow-900/20 text-yellow-400 border-yellow-400/20'}`}>
                      {parseFloat(overallRisk) >= 7 ? 'CRITICAL' : 'MEDIUM'}
                    </Badge>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Estimated Investigation Time</span>
                    <span className="font-mono text-cyan-400">
                      {parseFloat(overallRisk) >= 7 ? '15-30 min' : '5-10 min'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Recommended Action</span>
                    <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">
                      {parseFloat(overallRisk) >= 7 ? 'Block & Investigate' : 'Monitor'}
                    </Badge>
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <Button className="w-full bg-red-600 hover:bg-red-700">
                    Block Subscriber
                  </Button>
                  <Button variant="outline" className="w-full border-yellow-400/20 text-yellow-400">
                    Create Investigation Case
                  </Button>
                  <Button variant="outline" className="w-full border-blue-400/20 text-blue-400">
                    Export Evidence
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Evidence Collection */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-green-400">
                  <FileText className="w-5 h-5 mr-2" />
                  Evidence Collection
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Call Records</span>
                    <Badge className="bg-green-900/20 text-green-400 border-green-400/20">
                      Available
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">IMEI History</span>
                    <Badge className="bg-green-900/20 text-green-400 border-green-400/20">
                      Available
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Location Data</span>
                    <Badge className="bg-green-900/20 text-green-400 border-green-400/20">
                      Available
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Network Logs</span>
                    <Badge className="bg-green-900/20 text-green-400 border-green-400/20">
                      Available
                    </Badge>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-green-900/10 rounded-lg border border-green-400/20">
                  <p className="text-green-400 text-sm font-medium">Evidence Status</p>
                  <p className="text-green-300 text-xs mt-1">
                    All required evidence is available for investigation and legal proceedings.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-400">
                  <Zap className="w-5 h-5 mr-2" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start border-red-400/20 text-red-400">
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Add to Watchlist
                  </Button>

                  <Button variant="outline" className="w-full justify-start border-orange-400/20 text-orange-400">
                    <Bell className="w-4 h-4 mr-2" />
                    Set Alert Threshold
                  </Button>

                  <Button variant="outline" className="w-full justify-start border-yellow-400/20 text-yellow-400">
                    <Download className="w-4 h-4 mr-2" />
                    Download Report
                  </Button>

                  <Button variant="outline" className="w-full justify-start border-purple-400/20 text-purple-400">
                    <Users className="w-4 h-4 mr-2" />
                    Check Related Numbers
                  </Button>

                  <Button variant="outline" className="w-full justify-start border-cyan-400/20 text-cyan-400">
                    <MapPin className="w-4 h-4 mr-2" />
                    View Location History
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
