"use client"

import { useEffect, useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertTriangle,
  Shield,
  Eye,
  Target,
  Activity,
  Smartphone,
  PhoneIncoming,
  Users,
  BarChart3,
  Search,
  Filter,
  Bell,
  TrendingUp,
  TrendingDown,
  Clock,
  MapPin,
  Zap,
} from "lucide-react"
import { RealTimeAlerts, AlertSummary } from "@/components/real-time-alerts"

// Mock data for multiple subscribers with fraud indicators
const mockFraudData = [
  {
    msisdn: "+212333336",
    riskScore: 8.5,
    riskLevel: "HIGH",
    fraudTypes: ["SIMBOX", "IMEI_SWITCHING"],
    shortCalls: 85,
    imeiChanges: 5,
    noIncoming: true,
    lastActivity: "2 min ago",
    location: "Casablanca",
    priority: "CRITICAL"
  },
  {
    msisdn: "+212444447",
    riskScore: 7.2,
    riskLevel: "HIGH", 
    fraudTypes: ["HIGH_VOLUME", "GEOGRAPHIC_ANOMALY"],
    shortCalls: 45,
    imeiChanges: 2,
    noIncoming: false,
    lastActivity: "15 min ago",
    location: "Rabat",
    priority: "HIGH"
  },
  {
    msisdn: "+212555558",
    riskScore: 6.8,
    riskLevel: "MEDIUM",
    fraudTypes: ["SHORT_CALLS"],
    shortCalls: 32,
    imeiChanges: 1,
    noIncoming: false,
    lastActivity: "1 hour ago",
    location: "Marrakech",
    priority: "MEDIUM"
  },
  {
    msisdn: "+212666669",
    riskScore: 5.5,
    riskLevel: "MEDIUM",
    fraudTypes: ["UNUSUAL_PATTERN"],
    shortCalls: 18,
    imeiChanges: 0,
    noIncoming: false,
    lastActivity: "3 hours ago",
    location: "Fez",
    priority: "LOW"
  }
]

export default function FraudOverviewPage() {
  const [fraudData, setFraudData] = useState(mockFraudData)
  const [searchTerm, setSearchTerm] = useState("")
  const [riskFilter, setRiskFilter] = useState("all")
  const [timeRange, setTimeRange] = useState("24h")

  const filteredData = fraudData.filter(item => {
    const matchesSearch = item.msisdn.includes(searchTerm) || item.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRisk = riskFilter === "all" || item.riskLevel.toLowerCase() === riskFilter
    return matchesSearch && matchesRisk
  })

  const highRiskCount = fraudData.filter(item => item.riskLevel === "HIGH").length
  const criticalAlerts = fraudData.filter(item => item.priority === "CRITICAL").length
  const avgRiskScore = (fraudData.reduce((sum, item) => sum + item.riskScore, 0) / fraudData.length).toFixed(1)

  const getRiskColor = (level: string) => {
    switch(level) {
      case "HIGH": return "text-red-400 bg-red-900/20 border-red-400/20"
      case "MEDIUM": return "text-yellow-400 bg-yellow-900/20 border-yellow-400/20"
      default: return "text-green-400 bg-green-900/20 border-green-400/20"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch(priority) {
      case "CRITICAL": return "text-red-400"
      case "HIGH": return "text-orange-400"
      case "MEDIUM": return "text-yellow-400"
      default: return "text-green-400"
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Fraud Overview Dashboard</h1>
          <p className="text-gray-400 mt-2">Real-time fraud monitoring across all subscribers</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm" className="bg-red-900/20 border-red-400/20 text-red-400">
            <Bell className="w-4 h-4 mr-2" />
            {criticalAlerts} Critical Alerts
          </Button>
          <Button variant="outline" size="sm">
            <BarChart3 className="w-4 h-4 mr-2" />
            Export Dashboard
          </Button>
        </div>
      </div>

      {/* Alert Summary */}
      <AlertSummary />

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Monitored</p>
                <p className="text-3xl font-bold text-blue-400">{fraudData.length}</p>
                <p className="text-xs text-gray-500 mt-1">Active subscribers</p>
              </div>
              <Users className="w-10 h-10 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800 border-red-400/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">High Risk</p>
                <p className="text-3xl font-bold text-red-400">{highRiskCount}</p>
                <p className="text-xs text-gray-500 mt-1">Require immediate attention</p>
              </div>
              <AlertTriangle className="w-10 h-10 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Avg Risk Score</p>
                <p className="text-3xl font-bold text-yellow-400">{avgRiskScore}/10</p>
                <p className="text-xs text-gray-500 mt-1">Platform average</p>
              </div>
              <Shield className="w-10 h-10 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">SIMbox Detected</p>
                <p className="text-3xl font-bold text-orange-400">
                  {fraudData.filter(item => item.fraudTypes.includes("SIMBOX")).length}
                </p>
                <p className="text-xs text-gray-500 mt-1">Active cases</p>
              </div>
              <Target className="w-10 h-10 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter Controls */}
      <Card className="bg-gray-900 border-gray-800">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Search className="w-4 h-4 text-gray-400" />
                <Input 
                  placeholder="Search MSISDN or location..." 
                  className="w-64 bg-gray-800 border-gray-700"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={riskFilter} onValueChange={setRiskFilter}>
                <SelectTrigger className="w-32 bg-gray-800 border-gray-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Risk</SelectItem>
                  <SelectItem value="high">High Risk</SelectItem>
                  <SelectItem value="medium">Medium Risk</SelectItem>
                  <SelectItem value="low">Low Risk</SelectItem>
                </SelectContent>
              </Select>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32 bg-gray-800 border-gray-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="24h">Last 24h</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">
                <Activity className="w-3 h-3 mr-1" />
                Real-time
              </Badge>
              <Button size="sm" variant="outline" className="h-8">
                <Filter className="w-4 h-4 mr-1" />
                Advanced
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fraud Detection Table */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Eye className="w-5 h-5 mr-2" />
            Active Fraud Cases ({filteredData.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {filteredData.map((item, index) => (
              <div key={index} className="p-4 bg-gray-800/50 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="font-mono text-white font-medium">{item.msisdn}</p>
                      <p className="text-sm text-gray-400">{item.location}</p>
                    </div>
                    <Badge className={getRiskColor(item.riskLevel)}>
                      {item.riskLevel} RISK
                    </Badge>
                    <div className="flex flex-wrap gap-1">
                      {item.fraudTypes.map((type, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {type.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-6">
                    <div className="text-center">
                      <p className="text-sm text-gray-400">Risk Score</p>
                      <p className={`text-lg font-bold ${getPriorityColor(item.priority)}`}>
                        {item.riskScore}/10
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-400">Short Calls</p>
                      <p className="text-lg font-bold text-red-400">{item.shortCalls}%</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-400">IMEI Changes</p>
                      <p className="text-lg font-bold text-orange-400">{item.imeiChanges}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-400">No Incoming</p>
                      <p className={`text-lg font-bold ${item.noIncoming ? 'text-red-400' : 'text-green-400'}`}>
                        {item.noIncoming ? 'YES' : 'NO'}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-400">Last Activity</p>
                      <p className="text-sm text-cyan-400">{item.lastActivity}</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" className="border-blue-400/20 text-blue-400">
                        Investigate
                      </Button>
                      {item.priority === "CRITICAL" && (
                        <Button size="sm" className="bg-red-600 hover:bg-red-700">
                          Block
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Alerts */}
      <RealTimeAlerts />
    </div>
  )
}
