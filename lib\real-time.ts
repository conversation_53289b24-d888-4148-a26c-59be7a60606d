"use client"

export class RealTimeDataManager {
  private intervals: Map<string, NodeJS.Timeout> = new Map()
  private callbacks: Map<string, Function> = new Map()

  startRealTimeUpdates(endpoint: string, callback: Function, interval = 30000) {
    // Clear existing interval if any
    this.stopRealTimeUpdates(endpoint)

    // Store callback
    this.callbacks.set(endpoint, callback)

    // Create new interval
    const intervalId = setInterval(async () => {
      try {
        const response = await fetch(endpoint)
        const data = await response.json()
        callback(data)
      } catch (error) {
        console.error(`Error fetching real-time data from ${endpoint}:`, error)
      }
    }, interval)

    this.intervals.set(endpoint, intervalId)
  }

  stopRealTimeUpdates(endpoint: string) {
    const intervalId = this.intervals.get(endpoint)
    if (intervalId) {
      clearInterval(intervalId)
      this.intervals.delete(endpoint)
      this.callbacks.delete(endpoint)
    }
  }

  stopAllUpdates() {
    this.intervals.forEach((intervalId) => clearInterval(intervalId))
    this.intervals.clear()
    this.callbacks.clear()
  }

  updateInterval(endpoint: string, newInterval: number) {
    const callback = this.callbacks.get(endpoint)
    if (callback) {
      this.stopRealTimeUpdates(endpoint)
      this.startRealTimeUpdates(endpoint, callback, newInterval)
    }
  }
}

export const realTimeManager = new RealTimeDataManager()
