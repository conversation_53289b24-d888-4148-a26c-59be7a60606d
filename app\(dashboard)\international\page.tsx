"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Globe,
  Phone,
  PhoneOutgoing,
  PhoneIncoming,
  MapPin,
  TrendingUp,
  Users,
  DollarSign,
  BarChart3,
  RefreshCw,
  Download,
} from "lucide-react"

export default function InternationalPage() {
  const [internationalData, setInternationalData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  const fetchData = async () => {
    try {
      const response = await fetch("/api/international-call-activity.json")
      const data = await response.json()
      setInternationalData(data)
      setLastUpdate(new Date())
    } catch (error) {
      console.error("Error fetching international data:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()

    // Real-time updates every 45 seconds
    const interval = setInterval(fetchData, 45000)
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
      </div>
    )
  }

  // Mock country data based on the international calls
  const countryData = [
    { country: "United Kingdom", calls: 1, revenue: 45, flag: "🇬🇧" },
    { country: "France", calls: 1, revenue: 38, flag: "🇫🇷" },
    { country: "Germany", calls: 1, revenue: 42, flag: "🇩🇪" },
    { country: "China", calls: 1, revenue: 28, flag: "🇨🇳" },
    { country: "UAE", calls: 1, revenue: 35, flag: "🇦🇪" },
  ]

  const totalRevenue = countryData.reduce((sum, country) => sum + country.revenue, 0)

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">International Call Analytics</h1>
          <p className="text-gray-400 mt-2">Global communication patterns and revenue analysis</p>
          <p className="text-xs text-gray-500 mt-1">Last updated: {lastUpdate.toLocaleTimeString()}</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm" onClick={fetchData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-400/20">
            <Globe className="w-4 h-4 mr-1" />
            Live Data
          </Badge>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total International Calls</p>
                <p className="text-2xl font-bold text-green-400">{internationalData?.totalCalls}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <p className="text-sm text-green-400">+18.5%</p>
                </div>
              </div>
              <Globe className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Countries Reached</p>
                <p className="text-2xl font-bold text-blue-400">{countryData.length}</p>
                <p className="text-sm text-gray-400">Active destinations</p>
              </div>
              <MapPin className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">International Revenue</p>
                <p className="text-2xl font-bold text-purple-400">DH {totalRevenue}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-purple-400 mr-1" />
                  <p className="text-sm text-purple-400">+22.3%</p>
                </div>
              </div>
              <DollarSign className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Avg Call Duration</p>
                <p className="text-2xl font-bold text-orange-400">4.2m</p>
                <p className="text-sm text-gray-400">Per international call</p>
              </div>
              <Phone className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Call Direction Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-green-400">
              <PhoneOutgoing className="w-5 h-5 mr-2" />
              Outgoing International Calls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-4xl font-bold text-green-400 mb-2">{internationalData?.outgoing?.count || 0}</p>
              <p className="text-gray-400">Total Outgoing Calls</p>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Unique Numbers Called</span>
                <span className="font-mono text-green-400">{internationalData?.outgoing?.uniqueNumbers || 0}</span>
              </div>
              <Separator className="bg-gray-800" />

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Countries Called</span>
                <span className="font-mono text-green-400">{internationalData?.outgoing?.countries || 0}</span>
              </div>
              <Separator className="bg-gray-800" />

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Success Rate</span>
                <Badge className="bg-green-900/20 text-green-400 border-green-400/20">98.5%</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-400">
              <PhoneIncoming className="w-5 h-5 mr-2" />
              Incoming International Calls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-4xl font-bold text-blue-400 mb-2">{internationalData?.incoming?.count || 0}</p>
              <p className="text-gray-400">Total Incoming Calls</p>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Unique Numbers</span>
                <span className="font-mono text-blue-400">{internationalData?.incoming?.uniqueNumbers || 0}</span>
              </div>
              <Separator className="bg-gray-800" />

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Origin Countries</span>
                <span className="font-mono text-blue-400">{internationalData?.incoming?.countries || 0}</span>
              </div>
              <Separator className="bg-gray-800" />

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Answer Rate</span>
                <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">94.2%</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Country Breakdown */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center text-purple-400">
            <MapPin className="w-5 h-5 mr-2" />
            Country-wise Call Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {countryData.map((country, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{country.flag}</span>
                    <div>
                      <p className="font-medium text-white">{country.country}</p>
                      <p className="text-sm text-gray-400">
                        {country.calls} calls • DH {country.revenue} revenue
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-mono text-purple-400">DH {country.revenue}</p>
                    <p className="text-sm text-gray-400">{((country.revenue / totalRevenue) * 100).toFixed(1)}%</p>
                  </div>
                </div>
                <Progress value={(country.revenue / totalRevenue) * 100} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Analytics and Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-yellow-400">
              <BarChart3 className="w-5 h-5 mr-2" />
              Call Patterns
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Peak Calling Hours</span>
              <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">2-4 PM</Badge>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Most Called Region</span>
              <span className="font-mono text-yellow-400">Europe</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Avg Call Cost</span>
              <span className="font-mono text-yellow-400">
                DH {(totalRevenue / internationalData?.totalCalls).toFixed(1)}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Weekend vs Weekday</span>
              <span className="font-mono text-yellow-400">60:40</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-cyan-400">
              <Users className="w-5 h-5 mr-2" />
              Usage Statistics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Active International Users</span>
              <span className="font-mono text-cyan-400">
                {(internationalData?.outgoing?.uniqueNumbers || 0) + (internationalData?.incoming?.uniqueNumbers || 0)}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Repeat Callers</span>
              <span className="font-mono text-cyan-400">75%</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">New Destinations</span>
              <span className="font-mono text-cyan-400">2 this week</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Call Completion Rate</span>
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">96.8%</Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-orange-400">
              <DollarSign className="w-5 h-5 mr-2" />
              Revenue Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Revenue Growth</span>
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                <span className="font-mono text-green-400">+22.3%</span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Top Revenue Country</span>
              <span className="font-mono text-orange-400">🇬🇧 UK</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Monthly Projection</span>
              <span className="font-mono text-orange-400">DH {(totalRevenue * 4.3).toFixed(0)}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Profit Margin</span>
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">68%</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-gray-400">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start bg-transparent">
              <Globe className="w-4 h-4 mr-2" />
              View All Countries
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <BarChart3 className="w-4 h-4 mr-2" />
              Generate Report
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <DollarSign className="w-4 h-4 mr-2" />
              Revenue Analysis
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Phone className="w-4 h-4 mr-2" />
              Call Quality Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
