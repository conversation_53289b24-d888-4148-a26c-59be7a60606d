"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { MessageSquare, Send, Inbox, Globe, MapPin, TrendingUp, Users, BarChart3 } from "lucide-react"

export default function SMSPage() {
  const [smsData, setSmsData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/sms-activity.json")
        const data = await response.json()
        setSmsData(data)
      } catch (error) {
        console.error("Error fetching SMS data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-500"></div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">SMS Activity</h1>
          <p className="text-gray-400 mt-2">Text messaging analytics and usage patterns</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <BarChart3 className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total SMS</p>
                <p className="text-2xl font-bold text-purple-400">{smsData?.totalSMS}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <p className="text-sm text-green-400">+5.2%</p>
                </div>
              </div>
              <MessageSquare className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Outgoing</p>
                <p className="text-2xl font-bold text-green-400">
                  {smsData?.local.outgoing.count + smsData?.international.outgoing.count}
                </p>
              </div>
              <Send className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Incoming</p>
                <p className="text-2xl font-bold text-blue-400">
                  {smsData?.local.incoming.count + smsData?.international.incoming.count}
                </p>
              </div>
              <Inbox className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Success Rate</p>
                <p className="text-2xl font-bold text-orange-400">99.1%</p>
              </div>
              <BarChart3 className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Local vs International */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-400">
              <MapPin className="w-5 h-5 mr-2" />
              Local SMS Activity
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-blue-400 mb-2">
                {smsData?.local.outgoing.count + smsData?.local.incoming.count}
              </p>
              <p className="text-gray-400">Total Local SMS</p>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Outgoing SMS</span>
                <span className="font-mono text-green-400">{smsData?.local.outgoing.count}</span>
              </div>
              <Progress
                value={
                  (smsData?.local.outgoing.count / (smsData?.local.outgoing.count + smsData?.local.incoming.count)) *
                  100
                }
                className="h-2"
              />

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Incoming SMS</span>
                <span className="font-mono text-blue-400">{smsData?.local.incoming.count}</span>
              </div>
              <Progress
                value={
                  (smsData?.local.incoming.count / (smsData?.local.outgoing.count + smsData?.local.incoming.count)) *
                  100
                }
                className="h-2"
              />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-800">
              <div className="text-center">
                <p className="text-sm text-gray-400">Distinct Contacts (Out)</p>
                <p className="font-mono text-green-400">{smsData?.local.outgoing.distinctNumbers}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-400">Distinct Contacts (In)</p>
                <p className="font-mono text-blue-400">{smsData?.local.incoming.distinctNumbers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-green-400">
              <Globe className="w-5 h-5 mr-2" />
              International SMS Activity
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-green-400 mb-2">
                {smsData?.international.outgoing.count + smsData?.international.incoming.count}
              </p>
              <p className="text-gray-400">Total International SMS</p>
            </div>

            {smsData?.international.outgoing.count + smsData?.international.incoming.count === 0 ? (
              <div className="text-center py-8">
                <Globe className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-400">No international SMS activity</p>
                <p className="text-sm text-gray-500 mt-2">All messaging activity is local</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Outgoing SMS</span>
                  <span className="font-mono text-green-400">{smsData?.international.outgoing.count}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Incoming SMS</span>
                  <span className="font-mono text-blue-400">{smsData?.international.incoming.count}</span>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-800">
                  <div className="text-center">
                    <p className="text-sm text-gray-400">Distinct Contacts (Out)</p>
                    <p className="font-mono text-green-400">{smsData?.international.outgoing.distinctNumbers}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-400">Distinct Contacts (In)</p>
                    <p className="font-mono text-blue-400">{smsData?.international.incoming.distinctNumbers}</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-purple-400">
              <Users className="w-5 h-5 mr-2" />
              Contact Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Total Contacts</span>
              <span className="font-mono text-purple-400">
                {smsData?.local.outgoing.distinctNumbers + smsData?.local.incoming.distinctNumbers}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Active Conversations</span>
              <span className="font-mono text-cyan-400">
                {Math.min(smsData?.local.outgoing.distinctNumbers, smsData?.local.incoming.distinctNumbers)}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">One-way Contacts</span>
              <span className="font-mono text-yellow-400">
                {Math.abs(smsData?.local.outgoing.distinctNumbers - smsData?.local.incoming.distinctNumbers)}
              </span>
            </div>

            <div className="pt-4 border-t border-gray-800">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Avg SMS per Contact</span>
                <span className="font-mono text-orange-400">
                  {(
                    (smsData?.local.outgoing.count + smsData?.local.incoming.count) /
                    (smsData?.local.outgoing.distinctNumbers + smsData?.local.incoming.distinctNumbers)
                  ).toFixed(1)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-yellow-400">
              <TrendingUp className="w-5 h-5 mr-2" />
              Usage Patterns
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Peak Usage</span>
              <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-400/20">Evening</Badge>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Most Active Day</span>
              <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">Friday</Badge>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Response Rate</span>
              <span className="font-mono text-green-400">85%</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Avg Response Time</span>
              <span className="font-mono text-purple-400">12 min</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center text-cyan-400">
              <BarChart3 className="w-5 h-5 mr-2" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Delivery Rate</span>
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">99.1%</Badge>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Failed Messages</span>
              <span className="font-mono text-red-400">0.9%</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Avg Delivery Time</span>
              <span className="font-mono text-blue-400">1.2s</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Network Quality</span>
              <Badge className="bg-green-900/20 text-green-400 border-green-400/20">Excellent</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SMS Distribution Chart */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-orange-400">SMS Distribution Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Local SMS</span>
              <span className="font-mono text-blue-400">
                {smsData?.local.outgoing.count + smsData?.local.incoming.count} (
                {(((smsData?.local.outgoing.count + smsData?.local.incoming.count) / smsData?.totalSMS) * 100).toFixed(
                  1,
                )}
                %)
              </span>
            </div>
            <Progress
              value={((smsData?.local.outgoing.count + smsData?.local.incoming.count) / smsData?.totalSMS) * 100}
              className="h-3"
            />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">International SMS</span>
              <span className="font-mono text-green-400">
                {smsData?.international.outgoing.count + smsData?.international.incoming.count} (
                {(
                  ((smsData?.international.outgoing.count + smsData?.international.incoming.count) /
                    smsData?.totalSMS) *
                  100
                ).toFixed(1)}
                %)
              </span>
            </div>
            <Progress
              value={
                ((smsData?.international.outgoing.count + smsData?.international.incoming.count) / smsData?.totalSMS) *
                100
              }
              className="h-3"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
