"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import {
  Search,
  Filter,
  Download,
  Eye,
  AlertTriangle,
  Clock,
  MapPin,
  Smartphone,
  Users,
  Target,
  BarChart3,
  Calendar,
  FileText,
  Zap,
  Shield,
} from "lucide-react"

interface AdvancedFiltersProps {
  onFiltersChange: (filters: FraudFilters) => void
}

export interface FraudFilters {
  riskScoreRange: [number, number]
  fraudTypes: string[]
  timeRange: string
  location: string
  imeiChanges: number
  callVolumeMin: number
  shortCallThreshold: number
  noIncomingOnly: boolean
  geographicAnomalies: boolean
  deviceTypes: string[]
  investigationStatus: string[]
}

export function AdvancedFraudFilters({ onFiltersChange }: AdvancedFiltersProps) {
  const [filters, setFilters] = useState<FraudFilters>({
    riskScoreRange: [0, 10],
    fraudTypes: [],
    timeRange: "24h",
    location: "",
    imeiChanges: 0,
    callVolumeMin: 0,
    shortCallThreshold: 30,
    noIncomingOnly: false,
    geographicAnomalies: false,
    deviceTypes: [],
    investigationStatus: []
  })

  const updateFilters = (newFilters: Partial<FraudFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    onFiltersChange(updatedFilters)
  }

  const fraudTypeOptions = [
    "SIMBOX",
    "IMEI_SWITCHING", 
    "GEOGRAPHIC_ANOMALY",
    "HIGH_VOLUME",
    "SHORT_CALLS",
    "NO_INCOMING",
    "CIRCULAR_CALLING",
    "BURST_PATTERN"
  ]

  const deviceTypeOptions = [
    "Android",
    "iPhone", 
    "Feature Phone",
    "Unknown",
    "Multiple Types"
  ]

  const statusOptions = [
    "NEW",
    "INVESTIGATING", 
    "BLOCKED",
    "CLEARED",
    "ESCALATED"
  ]

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="flex items-center text-white">
          <Filter className="w-5 h-5 mr-2" />
          Advanced Fraud Filters
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="risk-filters" className="space-y-4">
          <TabsList className="bg-gray-800">
            <TabsTrigger value="risk-filters">Risk & Patterns</TabsTrigger>
            <TabsTrigger value="behavior-filters">Behavior</TabsTrigger>
            <TabsTrigger value="device-filters">Device & Location</TabsTrigger>
            <TabsTrigger value="investigation-filters">Investigation</TabsTrigger>
          </TabsList>

          <TabsContent value="risk-filters" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Risk Score Range */}
              <div className="space-y-2">
                <Label className="text-gray-300">Risk Score Range</Label>
                <div className="px-3">
                  <Slider
                    value={filters.riskScoreRange}
                    onValueChange={(value) => updateFilters({ riskScoreRange: value as [number, number] })}
                    max={10}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>{filters.riskScoreRange[0]}</span>
                    <span>{filters.riskScoreRange[1]}</span>
                  </div>
                </div>
              </div>

              {/* Time Range */}
              <div className="space-y-2">
                <Label className="text-gray-300">Time Range</Label>
                <Select value={filters.timeRange} onValueChange={(value) => updateFilters({ timeRange: value })}>
                  <SelectTrigger className="bg-gray-800 border-gray-700">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1h">Last Hour</SelectItem>
                    <SelectItem value="24h">Last 24 Hours</SelectItem>
                    <SelectItem value="7d">Last 7 Days</SelectItem>
                    <SelectItem value="30d">Last 30 Days</SelectItem>
                    <SelectItem value="custom">Custom Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Fraud Types */}
            <div className="space-y-2">
              <Label className="text-gray-300">Fraud Types</Label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {fraudTypeOptions.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={type}
                      checked={filters.fraudTypes.includes(type)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({ fraudTypes: [...filters.fraudTypes, type] })
                        } else {
                          updateFilters({ fraudTypes: filters.fraudTypes.filter(t => t !== type) })
                        }
                      }}
                    />
                    <Label htmlFor={type} className="text-sm text-gray-300">
                      {type.replace('_', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="behavior-filters" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Short Call Threshold */}
              <div className="space-y-2">
                <Label className="text-gray-300">Short Call Threshold (%)</Label>
                <div className="px-3">
                  <Slider
                    value={[filters.shortCallThreshold]}
                    onValueChange={(value) => updateFilters({ shortCallThreshold: value[0] })}
                    max={100}
                    min={0}
                    step={5}
                    className="w-full"
                  />
                  <div className="text-center text-xs text-gray-400 mt-1">
                    {filters.shortCallThreshold}%
                  </div>
                </div>
              </div>

              {/* Minimum Call Volume */}
              <div className="space-y-2">
                <Label className="text-gray-300">Minimum Call Volume</Label>
                <Input
                  type="number"
                  value={filters.callVolumeMin}
                  onChange={(e) => updateFilters({ callVolumeMin: parseInt(e.target.value) || 0 })}
                  className="bg-gray-800 border-gray-700"
                  placeholder="0"
                />
              </div>
            </div>

            {/* Behavior Flags */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="noIncoming"
                  checked={filters.noIncomingOnly}
                  onCheckedChange={(checked) => updateFilters({ noIncomingOnly: !!checked })}
                />
                <Label htmlFor="noIncoming" className="text-gray-300">
                  No Incoming Calls Only
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="geoAnomalies"
                  checked={filters.geographicAnomalies}
                  onCheckedChange={(checked) => updateFilters({ geographicAnomalies: !!checked })}
                />
                <Label htmlFor="geoAnomalies" className="text-gray-300">
                  Geographic Anomalies Detected
                </Label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="device-filters" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Location Filter */}
              <div className="space-y-2">
                <Label className="text-gray-300">Location</Label>
                <Input
                  value={filters.location}
                  onChange={(e) => updateFilters({ location: e.target.value })}
                  className="bg-gray-800 border-gray-700"
                  placeholder="City, region, or cell tower"
                />
              </div>

              {/* IMEI Changes */}
              <div className="space-y-2">
                <Label className="text-gray-300">Minimum IMEI Changes</Label>
                <Input
                  type="number"
                  value={filters.imeiChanges}
                  onChange={(e) => updateFilters({ imeiChanges: parseInt(e.target.value) || 0 })}
                  className="bg-gray-800 border-gray-700"
                  placeholder="0"
                />
              </div>
            </div>

            {/* Device Types */}
            <div className="space-y-2">
              <Label className="text-gray-300">Device Types</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {deviceTypeOptions.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`device-${type}`}
                      checked={filters.deviceTypes.includes(type)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({ deviceTypes: [...filters.deviceTypes, type] })
                        } else {
                          updateFilters({ deviceTypes: filters.deviceTypes.filter(t => t !== type) })
                        }
                      }}
                    />
                    <Label htmlFor={`device-${type}`} className="text-sm text-gray-300">
                      {type}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="investigation-filters" className="space-y-4">
            {/* Investigation Status */}
            <div className="space-y-2">
              <Label className="text-gray-300">Investigation Status</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {statusOptions.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}`}
                      checked={filters.investigationStatus.includes(status)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({ investigationStatus: [...filters.investigationStatus, status] })
                        } else {
                          updateFilters({ investigationStatus: filters.investigationStatus.filter(s => s !== status) })
                        }
                      }}
                    />
                    <Label htmlFor={`status-${status}`} className="text-sm text-gray-300">
                      {status}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Filter Presets */}
            <div className="space-y-2">
              <Label className="text-gray-300">Quick Presets</Label>
              <div className="flex flex-wrap gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="border-red-400/20 text-red-400"
                  onClick={() => updateFilters({
                    riskScoreRange: [7, 10],
                    fraudTypes: ["SIMBOX", "IMEI_SWITCHING"],
                    noIncomingOnly: true
                  })}
                >
                  <Target className="w-3 h-3 mr-1" />
                  High Risk SIMbox
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  className="border-orange-400/20 text-orange-400"
                  onClick={() => updateFilters({
                    imeiChanges: 3,
                    fraudTypes: ["IMEI_SWITCHING"],
                    geographicAnomalies: true
                  })}
                >
                  <Smartphone className="w-3 h-3 mr-1" />
                  Device Switching
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  className="border-yellow-400/20 text-yellow-400"
                  onClick={() => updateFilters({
                    shortCallThreshold: 50,
                    callVolumeMin: 20,
                    fraudTypes: ["HIGH_VOLUME", "SHORT_CALLS"]
                  })}
                >
                  <BarChart3 className="w-3 h-3 mr-1" />
                  High Volume
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  className="border-purple-400/20 text-purple-400"
                  onClick={() => updateFilters({
                    investigationStatus: ["NEW"],
                    riskScoreRange: [5, 10]
                  })}
                >
                  <Eye className="w-3 h-3 mr-1" />
                  Needs Investigation
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-700">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const defaultFilters: FraudFilters = {
                riskScoreRange: [0, 10],
                fraudTypes: [],
                timeRange: "24h",
                location: "",
                imeiChanges: 0,
                callVolumeMin: 0,
                shortCallThreshold: 30,
                noIncomingOnly: false,
                geographicAnomalies: false,
                deviceTypes: [],
                investigationStatus: []
              }
              setFilters(defaultFilters)
              onFiltersChange(defaultFilters)
            }}
          >
            Clear All
          </Button>
          
          <div className="flex space-x-2">
            <Button size="sm" variant="outline" className="border-blue-400/20 text-blue-400">
              <Download className="w-3 h-3 mr-1" />
              Export Results
            </Button>
            <Button size="sm" className="bg-green-600 hover:bg-green-700">
              <Search className="w-3 h-3 mr-1" />
              Apply Filters
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function InvestigationWorkflow() {
  const [currentStep, setCurrentStep] = useState(1)
  
  const steps = [
    { id: 1, title: "Initial Assessment", icon: Eye },
    { id: 2, title: "Evidence Collection", icon: FileText },
    { id: 3, title: "Pattern Analysis", icon: BarChart3 },
    { id: 4, title: "Risk Evaluation", icon: Shield },
    { id: 5, title: "Action Decision", icon: Zap }
  ]

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="flex items-center text-white">
          <FileText className="w-5 h-5 mr-2" />
          Investigation Workflow
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Progress Steps */}
        <div className="flex items-center justify-between mb-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                currentStep >= step.id 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'border-gray-600 text-gray-400'
              }`}>
                <step.icon className="w-5 h-5" />
              </div>
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-2 ${
                  currentStep > step.id ? 'bg-blue-600' : 'bg-gray-600'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Current Step Content */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">
            Step {currentStep}: {steps[currentStep - 1].title}
          </h3>
          
          {currentStep === 1 && (
            <div className="space-y-3">
              <p className="text-gray-300">Review initial fraud indicators and risk score</p>
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" className="justify-start">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  View Risk Summary
                </Button>
                <Button variant="outline" className="justify-start">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Check Pattern Analysis
                </Button>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-3">
              <p className="text-gray-300">Collect all relevant evidence for investigation</p>
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" className="justify-start">
                  <Clock className="w-4 h-4 mr-2" />
                  Call Records
                </Button>
                <Button variant="outline" className="justify-start">
                  <MapPin className="w-4 h-4 mr-2" />
                  Location Data
                </Button>
                <Button variant="outline" className="justify-start">
                  <Smartphone className="w-4 h-4 mr-2" />
                  Device History
                </Button>
                <Button variant="outline" className="justify-start">
                  <Users className="w-4 h-4 mr-2" />
                  Network Analysis
                </Button>
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between pt-4">
            <Button
              variant="outline"
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
            >
              Previous
            </Button>
            <Button
              onClick={() => setCurrentStep(Math.min(5, currentStep + 1))}
              disabled={currentStep === 5}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {currentStep === 5 ? 'Complete Investigation' : 'Next'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
