"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { CreditCard, DollarSign, TrendingUp, Calendar, Receipt, Download, RefreshCw } from "lucide-react"

export default function RechargePage() {
  const [rechargeData, setRechargeData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/recharge-payment.json")
        const data = await response.json()
        setRechargeData(data)
      } catch (error) {
        console.error("Error fetching recharge data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Recharge & Payment</h1>
          <p className="text-gray-400 mt-2">Transaction history and payment analytics (Last 7 Days)</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Recharges</p>
                <p className="text-3xl font-bold text-green-400">{rechargeData?.totalRecharges}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <p className="text-sm text-green-400">+12.5%</p>
                </div>
              </div>
              <Receipt className="w-10 h-10 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Value</p>
                <p className="text-3xl font-bold text-blue-400">DH {rechargeData?.totalValueDH}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-blue-400 mr-1" />
                  <p className="text-sm text-blue-400">+8.3%</p>
                </div>
              </div>
              <DollarSign className="w-10 h-10 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Average Recharge</p>
                <p className="text-3xl font-bold text-purple-400">
                  DH {(rechargeData?.totalValueDH / rechargeData?.totalRecharges).toFixed(0)}
                </p>
                <div className="flex items-center mt-1">
                  <Calendar className="w-4 h-4 text-purple-400 mr-1" />
                  <p className="text-sm text-purple-400">Per transaction</p>
                </div>
              </div>
              <CreditCard className="w-10 h-10 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Transaction Details */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center text-green-400">
            <Receipt className="w-5 h-5 mr-2" />
            Recent Transactions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {rechargeData?.breakdown?.map((transaction: any, index: number) => (
              <div key={index}>
                <div className="flex items-center justify-between py-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-green-900/20 rounded-full flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <p className="font-medium text-white">Recharge Transaction #{index + 1}</p>
                      <p className="text-sm text-gray-400">{transaction.timestamp || "Timestamp not available"}</p>
                      <p className="text-xs text-gray-500 font-mono">Serial: {transaction.serial || "Not provided"}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-green-400">+DH {transaction.denomination}</p>
                    <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-400/20">
                      Completed
                    </Badge>
                  </div>
                </div>
                {index < rechargeData.breakdown.length - 1 && <Separator className="bg-gray-800" />}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-blue-400">Payment Distribution</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {rechargeData?.breakdown?.map((transaction: any, index: number) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-gray-400">DH {transaction.denomination}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-800 rounded-full h-2">
                    <div
                      className="bg-blue-400 h-2 rounded-full"
                      style={{
                        width: `${(transaction.denomination / rechargeData.totalValueDH) * 100}%`,
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-blue-400 font-mono">
                    {((transaction.denomination / rechargeData.totalValueDH) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-purple-400">Payment Insights</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Highest Recharge</span>
              <span className="font-mono text-green-400">
                DH {Math.max(...(rechargeData?.breakdown?.map((t: any) => t.denomination) || [0]))}
              </span>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Lowest Recharge</span>
              <span className="font-mono text-yellow-400">
                DH {Math.min(...(rechargeData?.breakdown?.map((t: any) => t.denomination) || [0]))}
              </span>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Payment Frequency</span>
              <Badge className="bg-blue-900/20 text-blue-400 border-blue-400/20">
                {(rechargeData?.totalRecharges / 7).toFixed(1)}/day
              </Badge>
            </div>
            <Separator className="bg-gray-800" />

            <div className="flex justify-between items-center">
              <span className="text-gray-400">Spending Pattern</span>
              <Badge className="bg-purple-900/20 text-purple-400 border-purple-400/20">Regular</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-yellow-400">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="justify-start bg-transparent">
              <CreditCard className="w-4 h-4 mr-2" />
              New Recharge
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Receipt className="w-4 h-4 mr-2" />
              View All Transactions
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Download className="w-4 h-4 mr-2" />
              Download Receipt
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Calendar className="w-4 h-4 mr-2" />
              Payment Schedule
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
