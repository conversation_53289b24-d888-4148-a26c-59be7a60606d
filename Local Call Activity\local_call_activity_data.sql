-- DML: Insert local call activity data
INSERT INTO local_call_activity (timestamp, caller_number, called_number, call_direction, duration, cell_id) VALUES
('2024-07-17 09:00:00', '9876543210', '9123456781', 'Outgoing', 200, 'CELL123'),
('2024-07-17 09:10:00', '9876543210', '9123456782', 'Outgoing', 180, 'CELL124'),
('2024-07-17 09:20:00', '9876543210', '9123456783', 'Outgoing', 240, 'CELL125'),
('2024-07-17 09:30:00', '9123456784', '9876543210', 'Incoming', 150, 'CELL126'),
('2024-07-17 09:40:00', '9123456785', '9876543210', 'Incoming', 210, 'CELL127'),
('2024-07-17 09:50:00', '9123456786', '9876543210', 'Incoming', 190, 'CELL128');
