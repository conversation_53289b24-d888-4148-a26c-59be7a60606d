"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { DollarSign, TrendingUp } from "lucide-react"

interface RevenueChartProps {
  totalValue: number
  breakdown: Array<{ denomination: number }>
}

export function RevenueChart({ totalValue, breakdown }: RevenueChartProps) {
  const maxValue = Math.max(...breakdown.map((item) => item.denomination))

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="flex items-center text-green-400">
          <DollarSign className="w-5 h-5 mr-2" />
          Revenue Breakdown
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-center">
            <p className="text-3xl font-bold text-green-400">DH {totalValue}</p>
            <p className="text-gray-400">Total Revenue</p>
            <div className="flex items-center justify-center mt-2">
              <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
              <span className="text-sm text-green-400">+12.5% from last week</span>
            </div>
          </div>

          <div className="space-y-3">
            {breakdown.map((item, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="w-8 text-sm text-gray-400">#{index + 1}</div>
                <div className="flex-1">
                  <div
                    className="h-8 bg-gradient-to-r from-green-600 to-green-400 rounded flex items-center px-3"
                    style={{ width: `${(item.denomination / maxValue) * 100}%` }}
                  >
                    <span className="text-white font-medium">DH {item.denomination}</span>
                  </div>
                </div>
                <div className="w-16 text-sm text-gray-400">{((item.denomination / totalValue) * 100).toFixed(1)}%</div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
